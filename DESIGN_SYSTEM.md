# Design System - AdAgent

Este documento descreve o Design System implementado para a plataforma AdAgent, baseado em Tailwind CSS e componentes React reutilizáveis.

## 🎨 Paleta de Cores

### Cores Primárias (Azul Escuro)
- `primary-50`: #eff6ff (<PERSON>ito claro)
- `primary-500`: #3b82f6 (Base)
- `primary-600`: #2563eb (Padrão)
- `primary-700`: #1d4ed8 (Escuro)
- `primary-900`: #1e3a8a (Muito escuro)

### Cores Secundárias (Ciano)
- `secondary-50`: #ecfeff (Muito claro)
- `secondary-500`: #06b6d4 (Base)
- `secondary-600`: #0891b2 (Padrão)
- `secondary-700`: #0e7490 (Escuro)

### Cores Neutras (Tons de Cinza)
- `neutral-50`: #f8fafc (Background claro)
- `neutral-100`: #f1f5f9 (Background)
- `neutral-500`: #64748b (Texto secundário)
- `neutral-700`: #334155 (Texto)
- `neutral-900`: #0f172a (Texto escuro)

### Cores de Estado
- **Sucesso**: `success-500` (#22c55e), `success-600` (#16a34a)
- **Aviso**: `warning-500` (#f59e0b), `warning-600` (#d97706)
- **Erro**: `error-500` (#ef4444), `error-600` (#dc2626)

## 📝 Tipografia

### Fontes
- **Sans-serif**: Inter (padrão)
- **Monospace**: JetBrains Mono

### Tamanhos
- `text-xs`: 0.75rem (12px)
- `text-sm`: 0.875rem (14px)
- `text-base`: 1rem (16px) - padrão
- `text-lg`: 1.125rem (18px)
- `text-xl`: 1.25rem (20px)
- `text-2xl`: 1.5rem (24px)
- `text-3xl`: 1.875rem (30px)

## 🧩 Componentes

### Button
Botão reutilizável com múltiplas variantes e tamanhos.

**Variantes:**
- `primary`: Botão principal (azul)
- `secondary`: Botão secundário (ciano)
- `outline`: Botão com borda
- `ghost`: Botão transparente
- `destructive`: Botão de ação destrutiva (vermelho)
- `success`: Botão de sucesso (verde)

**Tamanhos:**
- `sm`: Pequeno (h-8)
- `default`: Padrão (h-10)
- `lg`: Grande (h-12)
- `xl`: Extra grande (h-14)
- `icon`: Apenas ícone (h-10 w-10)

**Exemplo de uso:**
```tsx
<Button variant="primary" size="lg" loading={isLoading}>
  Salvar
</Button>
```

### Input
Campo de entrada com suporte a labels, ícones e estados de erro.

**Variantes:**
- `default`: Padrão
- `error`: Estado de erro
- `success`: Estado de sucesso

**Exemplo de uso:**
```tsx
<Input
  label="Email"
  type="email"
  placeholder="<EMAIL>"
  error="Email é obrigatório"
  leftIcon={<EmailIcon />}
/>
```

### Card
Componente de cartão flexível com header, content e footer.

**Variantes:**
- `default`: Padrão com sombra suave
- `elevated`: Sombra elevada com hover
- `outlined`: Apenas borda
- `ghost`: Transparente

**Exemplo de uso:**
```tsx
<Card variant="elevated">
  <CardHeader>
    <CardTitle>Título do Card</CardTitle>
  </CardHeader>
  <CardContent>
    Conteúdo do card...
  </CardContent>
</Card>
```

### Spinner
Indicador de carregamento com diferentes tamanhos e variantes.

**Exemplo de uso:**
```tsx
<Spinner size="lg" label="Carregando..." />
<LoadingScreen message="Processando dados..." />
<LoadingOverlay isVisible={loading} />
```

## 🏗️ Layout

### Layout Principal
O layout principal inclui:
- **Sidebar**: Navegação lateral fixa com links principais
- **Header**: Cabeçalho superior com informações do usuário
- **Main Content**: Área principal de conteúdo

**Estrutura:**
```tsx
<Layout>
  <YourPageContent />
</Layout>
```

### Navegação
Links principais:
- Dashboard (/)
- Campanhas (/campanhas)
- Ferramentas (/ferramentas)

## 🎭 Animações

### Classes Personalizadas
- `animate-fade-in`: Fade in suave (0.5s)
- `animate-slide-in`: Slide in de cima (0.3s)
- `animate-bounce-soft`: Bounce suave contínuo

### Transições
- Todos os componentes interativos têm `transition-all duration-200`
- Hover states com mudanças suaves de cor e sombra

## 📐 Espaçamento

### Grid System
- Uso de CSS Grid e Flexbox
- Breakpoints responsivos: `sm`, `md`, `lg`, `xl`
- Gaps padrão: `gap-4`, `gap-6`

### Padding/Margin
- Escala padrão do Tailwind: 4px, 8px, 12px, 16px, 24px, 32px
- Espaçamentos customizados: `space-y-6` para seções

## 🔧 Utilitários

### Função `cn()`
Utilitário para combinar classes CSS de forma condicional:

```tsx
import { cn } from '../lib/utils';

className={cn(
  'base-classes',
  condition && 'conditional-classes',
  className
)}
```

### Shadows Personalizadas
- `shadow-soft`: Sombra suave
- `shadow-medium`: Sombra média
- `shadow-strong`: Sombra forte

## 📱 Responsividade

### Breakpoints
- `sm`: 640px+
- `md`: 768px+
- `lg`: 1024px+
- `xl`: 1280px+

### Mobile First
- Design mobile-first
- Sidebar colapsável em telas pequenas
- Grid responsivo automático

## 🎯 Boas Práticas

1. **Consistência**: Use sempre os componentes do Design System
2. **Acessibilidade**: Todos os componentes incluem ARIA labels
3. **Performance**: Componentes otimizados com React.forwardRef
4. **Tipagem**: TypeScript completo em todos os componentes
5. **Flexibilidade**: Props customizáveis mantendo consistência visual

## 🚀 Como Usar

1. Importe os componentes necessários:
```tsx
import { Button, Card, Input } from './components/ui';
```

2. Use as classes do Tailwind para espaçamento e layout:
```tsx
<div className="space-y-6">
  <Card variant="elevated">
    {/* conteúdo */}
  </Card>
</div>
```

3. Mantenha consistência com as cores e tipografia definidas no sistema.
