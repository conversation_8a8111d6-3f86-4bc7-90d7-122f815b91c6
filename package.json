{"name": "ad-agent6-gemini", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:app\" \"npm run dev:server\"", "dev:app": "next dev --turbopack", "dev:server": "nodemon --exec ts-node server/index.ts", "build": "next build --turbopack", "build:server": "tsc server/index.ts --outDir dist", "start": "next start", "start:server": "node dist/index.js", "lint": "eslint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^6.14.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "next": "15.5.0", "prisma": "^6.14.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.2.1", "eslint": "^9", "eslint-config-next": "15.5.0", "nodemon": "^3.1.10", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}