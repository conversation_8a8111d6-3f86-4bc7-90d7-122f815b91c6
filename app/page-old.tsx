'use client';

import { useState, useEffect } from 'react';
import { apiClient, User, Post } from './lib/api';
import UserCard from './components/UserCard';
import PostCard from './components/PostCard';
import { <PERSON>ton, Card, CardHeader, CardTitle, CardContent, Input, Spinner } from './components/ui';

export default function Home() {
  const [users, setUsers] = useState<User[]>([]);
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'users' | 'posts'>('users');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [usersData, postsData] = await Promise.all([
        apiClient.getUsers(),
        apiClient.getPosts(),
      ]);
      setUsers(usersData);
      setPosts(postsData);
    } catch (err) {
      setError('Falha ao carregar dados. Certifique-se de que o servidor está rodando.');
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (confirm('Tem certeza que deseja deletar este usuário?')) {
      try {
        await apiClient.deleteUser(userId);
        setUsers(users.filter(user => user.id !== userId));
      } catch (err) {
        console.error('Error deleting user:', err);
        alert('Falha ao deletar usuário');
      }
    }
  };

  const handleDeletePost = async (postId: string) => {
    if (confirm('Tem certeza que deseja deletar este post?')) {
      try {
        await apiClient.deletePost(postId);
        setPosts(posts.filter(post => post.id !== postId));
      } catch (err) {
        console.error('Error deleting post:', err);
        alert('Falha ao deletar post');
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Full-Stack App
          </h1>
          <p className="text-gray-600">
            Next.js + Express + Prisma + PostgreSQL
          </p>
        </header>

        {/* Tab Navigation */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('users')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'users'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
            >
              Users ({users.length})
            </button>
            <button
              onClick={() => setActiveTab('posts')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'posts'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
            >
              Posts ({posts.length})
            </button>
          </nav>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4">
                  <button
                    onClick={fetchData}
                    className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div>
            {activeTab === 'users' && (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {users.length === 0 ? (
                  <div className="col-span-full text-center py-12">
                    <p className="text-gray-500">No users found</p>
                  </div>
                ) : (
                  users.map((user) => (
                    <UserCard
                      key={user.id}
                      user={user}
                      onDelete={handleDeleteUser}
                    />
                  ))
                )}
              </div>
            )}

            {activeTab === 'posts' && (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {posts.length === 0 ? (
                  <div className="col-span-full text-center py-12">
                    <p className="text-gray-500">No posts found</p>
                  </div>
                ) : (
                  posts.map((post) => (
                    <PostCard
                      key={post.id}
                      post={post}
                      onDelete={handleDeletePost}
                    />
                  ))
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
