'use client';

import { useState, useEffect } from 'react';
import { apiClient, User, Post } from './lib/api';
import UserCard from './components/UserCard';
import PostCard from './components/PostCard';
import { <PERSON>ton, Card, CardHeader, CardTitle, CardContent, Spinner } from './components/ui';

export default function Home() {
  const [users, setUsers] = useState<User[]>([]);
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'users' | 'posts'>('users');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const [usersData, postsData] = await Promise.all([
        apiClient.getUsers(),
        apiClient.getPosts(),
      ]);
      setUsers(usersData);
      setPosts(postsData);
    } catch (err) {
      setError('Falha ao carregar dados. Certifique-se de que o servidor está rodando.');
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (confirm('Tem certeza que deseja deletar este usuário?')) {
      try {
        await apiClient.deleteUser(userId);
        setUsers(users.filter(user => user.id !== userId));
      } catch (err) {
        console.error('Error deleting user:', err);
        alert('Falha ao deletar usuário');
      }
    }
  };

  const handleDeletePost = async (postId: string) => {
    if (confirm('Tem certeza que deseja deletar este post?')) {
      try {
        await apiClient.deletePost(postId);
        setPosts(posts.filter(post => post.id !== postId));
      } catch (err) {
        console.error('Error deleting post:', err);
        alert('Falha ao deletar post');
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Header com estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card variant="elevated">
          <CardHeader>
            <CardTitle>Total de Usuários</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-primary-600">{users.length}</div>
            <p className="text-sm text-neutral-500 mt-1">Usuários cadastrados</p>
          </CardContent>
        </Card>

        <Card variant="elevated">
          <CardHeader>
            <CardTitle>Total de Posts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-secondary-600">{posts.length}</div>
            <p className="text-sm text-neutral-500 mt-1">Posts publicados</p>
          </CardContent>
        </Card>

        <Card variant="elevated">
          <CardHeader>
            <CardTitle>Posts Publicados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-success-600">
              {posts.filter(post => post.published).length}
            </div>
            <p className="text-sm text-neutral-500 mt-1">Posts ativos</p>
          </CardContent>
        </Card>
      </div>

      {/* Controles */}
      <Card>
        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex space-x-4">
              <Button
                variant={activeTab === 'users' ? 'primary' : 'outline'}
                onClick={() => setActiveTab('users')}
              >
                Usuários ({users.length})
              </Button>
              <Button
                variant={activeTab === 'posts' ? 'primary' : 'outline'}
                onClick={() => setActiveTab('posts')}
              >
                Posts ({posts.length})
              </Button>
            </div>
            
            <div className="flex space-x-2">
              <Button variant="secondary" size="sm">
                Adicionar Usuário
              </Button>
              <Button variant="secondary" size="sm">
                Criar Post
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={fetchData}
                loading={loading}
              >
                Atualizar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Conteúdo */}
      {loading ? (
        <Card>
          <CardContent>
            <div className="flex justify-center items-center py-12">
              <Spinner size="lg" label="Carregando dados..." />
            </div>
          </CardContent>
        </Card>
      ) : error ? (
        <Card variant="outlined">
          <CardContent>
            <div className="text-center py-8">
              <div className="text-error-600 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-neutral-900 mb-2">Erro ao carregar dados</h3>
              <p className="text-neutral-600 mb-4">{error}</p>
              <Button onClick={fetchData} variant="primary">
                Tentar Novamente
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div>
          {activeTab === 'users' && (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {users.length === 0 ? (
                <Card className="col-span-full">
                  <CardContent>
                    <div className="text-center py-12">
                      <div className="text-neutral-400 mb-4">
                        <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-neutral-900 mb-2">Nenhum usuário encontrado</h3>
                      <p className="text-neutral-500">Comece adicionando alguns usuários ao sistema.</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                users.map((user) => (
                  <UserCard
                    key={user.id}
                    user={user}
                    onDelete={handleDeleteUser}
                  />
                ))
              )}
            </div>
          )}

          {activeTab === 'posts' && (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {posts.length === 0 ? (
                <Card className="col-span-full">
                  <CardContent>
                    <div className="text-center py-12">
                      <div className="text-neutral-400 mb-4">
                        <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-neutral-900 mb-2">Nenhum post encontrado</h3>
                      <p className="text-neutral-500">Comece criando alguns posts para o blog.</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                posts.map((post) => (
                  <PostCard
                    key={post.id}
                    post={post}
                    onDelete={handleDeletePost}
                  />
                ))
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
