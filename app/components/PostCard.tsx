'use client';

import { Post } from '../lib/api';

interface PostCardProps {
  post: Post;
  onEdit?: (post: Post) => void;
  onDelete?: (postId: string) => void;
}

export default function PostCard({ post, onEdit, onDelete }: PostCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-lg font-semibold text-gray-900">{post.title}</h3>
            {post.published ? (
              <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                Published
              </span>
            ) : (
              <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                Draft
              </span>
            )}
          </div>
          {post.content && (
            <p className="text-gray-600 mb-3 line-clamp-3">{post.content}</p>
          )}
          {post.author && (
            <p className="text-sm text-gray-500">
              By {post.author.name || post.author.email}
            </p>
          )}
        </div>
        <div className="flex space-x-2 ml-4">
          {onEdit && (
            <button
              onClick={() => onEdit(post)}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Edit
            </button>
          )}
          {onDelete && (
            <button
              onClick={() => onDelete(post.id)}
              className="text-red-600 hover:text-red-800 text-sm font-medium"
            >
              Delete
            </button>
          )}
        </div>
      </div>
      
      <div className="text-sm text-gray-500">
        <p>Created: {new Date(post.createdAt).toLocaleDateString()}</p>
        <p>Updated: {new Date(post.updatedAt).toLocaleDateString()}</p>
      </div>
    </div>
  );
}
