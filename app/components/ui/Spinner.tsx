import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';

const spinnerVariants = cva(
  'animate-spin rounded-full border-solid border-current border-r-transparent',
  {
    variants: {
      size: {
        xs: 'h-3 w-3 border',
        sm: 'h-4 w-4 border',
        default: 'h-6 w-6 border-2',
        lg: 'h-8 w-8 border-2',
        xl: 'h-12 w-12 border-4',
      },
      variant: {
        default: 'text-primary-600',
        secondary: 'text-secondary-600',
        neutral: 'text-neutral-600',
        white: 'text-white',
      },
    },
    defaultVariants: {
      size: 'default',
      variant: 'default',
    },
  }
);

export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  label?: string;
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size, variant, label, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('inline-flex items-center justify-center', className)}
        {...props}
      >
        <div
          className={cn(spinnerVariants({ size, variant }))}
          role="status"
          aria-label={label || 'Loading'}
        >
          <span className="sr-only">{label || 'Loading...'}</span>
        </div>
        {label && (
          <span className="ml-2 text-sm text-neutral-600">{label}</span>
        )}
      </div>
    );
  }
);

Spinner.displayName = 'Spinner';

// Componente de Loading Screen completo
export interface LoadingScreenProps {
  message?: string;
  size?: VariantProps<typeof spinnerVariants>['size'];
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = 'Carregando...', 
  size = 'xl' 
}) => {
  return (
    <div className="flex min-h-screen items-center justify-center bg-neutral-50">
      <div className="text-center">
        <Spinner size={size} className="mb-4" />
        <p className="text-neutral-600">{message}</p>
      </div>
    </div>
  );
};

// Componente de Loading Overlay
export interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  size?: VariantProps<typeof spinnerVariants>['size'];
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ 
  isVisible, 
  message = 'Carregando...', 
  size = 'lg' 
}) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm">
      <div className="rounded-xl bg-white p-6 shadow-strong">
        <div className="text-center">
          <Spinner size={size} className="mb-3" />
          <p className="text-sm text-neutral-600">{message}</p>
        </div>
      </div>
    </div>
  );
};

export { Spinner, LoadingScreen, LoadingOverlay, spinnerVariants };
