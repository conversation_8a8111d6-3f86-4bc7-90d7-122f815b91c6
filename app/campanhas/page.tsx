'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, Card<PERSON>ontent, <PERSON><PERSON> } from '../components/ui';

export default function CampanhasPage() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-neutral-900">Campanhas</h1>
          <p className="text-neutral-600 mt-1">Gerencie suas campanhas de marketing digital</p>
        </div>
        <Button variant="primary">
          Nova Campanha
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card variant="elevated">
          <CardHeader>
            <CardTitle>Campanhas Ativas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-success-600">12</div>
            <p className="text-sm text-neutral-500 mt-1">Em execução</p>
          </CardContent>
        </Card>

        <Card variant="elevated">
          <CardHeader>
            <CardTitle><PERSON>anhas <PERSON></CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-warning-600">3</div>
            <p className="text-sm text-neutral-500 mt-1">Temporariamente pausadas</p>
          </CardContent>
        </Card>

        <Card variant="elevated">
          <CardHeader>
            <CardTitle>Total de Impressões</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-primary-600">1.2M</div>
            <p className="text-sm text-neutral-500 mt-1">Últimos 30 dias</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Campanhas Recentes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((item) => (
              <div key={item} className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                <div>
                  <h3 className="font-medium text-neutral-900">Campanha de Verão {item}</h3>
                  <p className="text-sm text-neutral-500">Criada em 15 de Janeiro, 2024</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 text-xs font-medium bg-success-100 text-success-800 rounded-full">
                    Ativa
                  </span>
                  <Button variant="outline" size="sm">
                    Editar
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
