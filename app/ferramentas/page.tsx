'use client';

import { Card, CardHeader, CardTitle, CardContent, Button } from '../components/ui';

export default function FerramentasPage() {
  const tools = [
    {
      name: 'Gerador de Hashtags',
      description: 'Gere hashtags relevantes para suas postagens',
      icon: '🏷️',
      status: 'Disponível'
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON> de Sentimento',
      description: '<PERSON><PERSON><PERSON> o sentimento dos comentários e menções',
      icon: '😊',
      status: 'Disponível'
    },
    {
      name: 'Agendador de Posts',
      description: 'Agende suas postagens para múltiplas plataformas',
      icon: '📅',
      status: 'Em breve'
    },
    {
      name: '<PERSON><PERSON><PERSON> de Conteúdo',
      description: 'Crie conteúdo automaticamente com IA',
      icon: '✨',
      status: 'Beta'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-neutral-900">Ferramentas</h1>
          <p className="text-neutral-600 mt-1">Conjunto de ferramentas para otimizar seu marketing</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {tools.map((tool, index) => (
          <Card key={index} variant="elevated" className="hover:shadow-strong transition-shadow">
            <CardContent>
              <div className="flex items-start space-x-4">
                <div className="text-4xl">{tool.icon}</div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold text-neutral-900">{tool.name}</h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      tool.status === 'Disponível' 
                        ? 'bg-success-100 text-success-800'
                        : tool.status === 'Beta'
                        ? 'bg-warning-100 text-warning-800'
                        : 'bg-neutral-100 text-neutral-600'
                    }`}>
                      {tool.status}
                    </span>
                  </div>
                  <p className="text-neutral-600 mb-4">{tool.description}</p>
                  <Button 
                    variant={tool.status === 'Disponível' ? 'primary' : 'outline'}
                    size="sm"
                    disabled={tool.status === 'Em breve'}
                  >
                    {tool.status === 'Disponível' ? 'Usar Ferramenta' : 
                     tool.status === 'Beta' ? 'Testar Beta' : 'Em breve'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Estatísticas de Uso</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">156</div>
              <p className="text-sm text-neutral-500">Hashtags geradas</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary-600">89</div>
              <p className="text-sm text-neutral-500">Análises realizadas</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-success-600">23</div>
              <p className="text-sm text-neutral-500">Posts agendados</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
