{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/app/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\n\nexport interface User {\n  id: string;\n  email: string;\n  name?: string;\n  createdAt: string;\n  updatedAt: string;\n  posts?: Post[];\n}\n\nexport interface Post {\n  id: string;\n  title: string;\n  content?: string;\n  published: boolean;\n  authorId: string;\n  author?: {\n    id: string;\n    name?: string;\n    email: string;\n  };\n  createdAt: string;\n  updatedAt: string;\n}\n\nclass ApiClient {\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${API_BASE_URL}${endpoint}`;\n    \n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    const response = await fetch(url, config);\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return response.json();\n  }\n\n  // Users\n  async getUsers(): Promise<User[]> {\n    return this.request<User[]>('/api/users');\n  }\n\n  async getUserById(id: string): Promise<User> {\n    return this.request<User>(`/api/users/${id}`);\n  }\n\n  async createUser(userData: { email: string; name?: string }): Promise<User> {\n    return this.request<User>('/api/users', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async updateUser(id: string, userData: { email?: string; name?: string }): Promise<User> {\n    return this.request<User>(`/api/users/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async deleteUser(id: string): Promise<void> {\n    await this.request(`/api/users/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  // Posts\n  async getPosts(): Promise<Post[]> {\n    return this.request<Post[]>('/api/posts');\n  }\n\n  async getPostById(id: string): Promise<Post> {\n    return this.request<Post>(`/api/posts/${id}`);\n  }\n\n  async createPost(postData: {\n    title: string;\n    content?: string;\n    authorId: string;\n    published?: boolean;\n  }): Promise<Post> {\n    return this.request<Post>('/api/posts', {\n      method: 'POST',\n      body: JSON.stringify(postData),\n    });\n  }\n\n  async updatePost(id: string, postData: {\n    title?: string;\n    content?: string;\n    published?: boolean;\n  }): Promise<Post> {\n    return this.request<Post>(`/api/posts/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(postData),\n    });\n  }\n\n  async deletePost(id: string): Promise<void> {\n    await this.request(`/api/posts/${id}`, {\n      method: 'DELETE',\n    });\n  }\n}\n\nexport const apiClient = new ApiClient();\n"], "names": [], "mappings": ";;;;AAAqB;AAArB,MAAM,eAAe,2KAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AA0BxD,MAAM;IACJ,MAAc,QACZ,QAAgB,EAEJ;YADZ,UAAA,iEAAuB,CAAC;QAExB,MAAM,MAAM,AAAC,GAAiB,OAAf,cAAwB,OAAT;QAE9B,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,uBAAsC,OAAhB,SAAS,MAAM;QACxD;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,MAAM,WAA4B;QAChC,OAAO,IAAI,CAAC,OAAO,CAAS;IAC9B;IAEA,MAAM,YAAY,EAAU,EAAiB;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAO,AAAC,cAAgB,OAAH;IAC1C;IAEA,MAAM,WAAW,QAA0C,EAAiB;QAC1E,OAAO,IAAI,CAAC,OAAO,CAAO,cAAc;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAA2C,EAAiB;QACvF,OAAO,IAAI,CAAC,OAAO,CAAO,AAAC,cAAgB,OAAH,KAAM;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,IAAI,CAAC,OAAO,CAAC,AAAC,cAAgB,OAAH,KAAM;YACrC,QAAQ;QACV;IACF;IAEA,QAAQ;IACR,MAAM,WAA4B;QAChC,OAAO,IAAI,CAAC,OAAO,CAAS;IAC9B;IAEA,MAAM,YAAY,EAAU,EAAiB;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAO,AAAC,cAAgB,OAAH;IAC1C;IAEA,MAAM,WAAW,QAKhB,EAAiB;QAChB,OAAO,IAAI,CAAC,OAAO,CAAO,cAAc;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAI5B,EAAiB;QAChB,OAAO,IAAI,CAAC,OAAO,CAAO,AAAC,cAAgB,OAAH,KAAM;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,IAAI,CAAC,OAAO,CAAC,AAAC,cAAgB,OAAH,KAAM;YACrC,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/app/components/UserCard.tsx"], "sourcesContent": ["'use client';\n\nimport { User } from '../lib/api';\n\ninterface UserCardProps {\n  user: User;\n  onEdit?: (user: User) => void;\n  onDelete?: (userId: string) => void;\n}\n\nexport default function UserCard({ user, onEdit, onDelete }: UserCardProps) {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n      <div className=\"flex justify-between items-start mb-4\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            {user.name || 'Unnamed User'}\n          </h3>\n          <p className=\"text-gray-600\">{user.email}</p>\n        </div>\n        <div className=\"flex space-x-2\">\n          {onEdit && (\n            <button\n              onClick={() => onEdit(user)}\n              className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n            >\n              Edit\n            </button>\n          )}\n          {onDelete && (\n            <button\n              onClick={() => onDelete(user.id)}\n              className=\"text-red-600 hover:text-red-800 text-sm font-medium\"\n            >\n              Delete\n            </button>\n          )}\n        </div>\n      </div>\n      \n      <div className=\"text-sm text-gray-500\">\n        <p>Created: {new Date(user.createdAt).toLocaleDateString()}</p>\n        {user.posts && (\n          <p className=\"mt-1\">Posts: {user.posts.length}</p>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAUe,SAAS,SAAS,KAAyC;QAAzC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAiB,GAAzC;IAC/B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CACX,KAAK,IAAI,IAAI;;;;;;0CAEhB,6LAAC;gCAAE,WAAU;0CAAiB,KAAK,KAAK;;;;;;;;;;;;kCAE1C,6LAAC;wBAAI,WAAU;;4BACZ,wBACC,6LAAC;gCACC,SAAS,IAAM,OAAO;gCACtB,WAAU;0CACX;;;;;;4BAIF,0BACC,6LAAC;gCACC,SAAS,IAAM,SAAS,KAAK,EAAE;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAE;4BAAU,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;oBACvD,KAAK,KAAK,kBACT,6LAAC;wBAAE,WAAU;;4BAAO;4BAAQ,KAAK,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;;;AAKvD;KAtCwB", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/app/components/PostCard.tsx"], "sourcesContent": ["'use client';\n\nimport { Post } from '../lib/api';\n\ninterface PostCardProps {\n  post: Post;\n  onEdit?: (post: Post) => void;\n  onDelete?: (postId: string) => void;\n}\n\nexport default function PostCard({ post, onEdit, onDelete }: PostCardProps) {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n      <div className=\"flex justify-between items-start mb-4\">\n        <div className=\"flex-1\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">{post.title}</h3>\n            {post.published ? (\n              <span className=\"px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full\">\n                Published\n              </span>\n            ) : (\n              <span className=\"px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full\">\n                Draft\n              </span>\n            )}\n          </div>\n          {post.content && (\n            <p className=\"text-gray-600 mb-3 line-clamp-3\">{post.content}</p>\n          )}\n          {post.author && (\n            <p className=\"text-sm text-gray-500\">\n              By {post.author.name || post.author.email}\n            </p>\n          )}\n        </div>\n        <div className=\"flex space-x-2 ml-4\">\n          {onEdit && (\n            <button\n              onClick={() => onEdit(post)}\n              className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n            >\n              Edit\n            </button>\n          )}\n          {onDelete && (\n            <button\n              onClick={() => onDelete(post.id)}\n              className=\"text-red-600 hover:text-red-800 text-sm font-medium\"\n            >\n              Delete\n            </button>\n          )}\n        </div>\n      </div>\n      \n      <div className=\"text-sm text-gray-500\">\n        <p>Created: {new Date(post.createdAt).toLocaleDateString()}</p>\n        <p>Updated: {new Date(post.updatedAt).toLocaleDateString()}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAUe,SAAS,SAAS,KAAyC;QAAzC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAiB,GAAzC;IAC/B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAuC,KAAK,KAAK;;;;;;oCAC9D,KAAK,SAAS,iBACb,6LAAC;wCAAK,WAAU;kDAAyE;;;;;6DAIzF,6LAAC;wCAAK,WAAU;kDAA2E;;;;;;;;;;;;4BAK9F,KAAK,OAAO,kBACX,6LAAC;gCAAE,WAAU;0CAAmC,KAAK,OAAO;;;;;;4BAE7D,KAAK,MAAM,kBACV,6LAAC;gCAAE,WAAU;;oCAAwB;oCAC/B,KAAK,MAAM,CAAC,IAAI,IAAI,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;;kCAI/C,6LAAC;wBAAI,WAAU;;4BACZ,wBACC,6LAAC;gCACC,SAAS,IAAM,OAAO;gCACtB,WAAU;0CACX;;;;;;4BAIF,0BACC,6LAAC;gCACC,SAAS,IAAM,SAAS,KAAK,EAAE;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAE;4BAAU,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;kCACxD,6LAAC;;4BAAE;4BAAU,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;AAIhE;KApDwB", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { apiClient, User, Post } from './lib/api';\nimport UserCard from './components/UserCard';\nimport PostCard from './components/PostCard';\n\nexport default function Home() {\n  const [users, setUsers] = useState<User[]>([]);\n  const [posts, setPosts] = useState<Post[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState<'users' | 'posts'>('users');\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const [usersData, postsData] = await Promise.all([\n        apiClient.getUsers(),\n        apiClient.getPosts(),\n      ]);\n      setUsers(usersData);\n      setPosts(postsData);\n    } catch (err) {\n      setError('Failed to fetch data. Make sure the server is running.');\n      console.error('Error fetching data:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteUser = async (userId: string) => {\n    if (confirm('Are you sure you want to delete this user?')) {\n      try {\n        await apiClient.deleteUser(userId);\n        setUsers(users.filter(user => user.id !== userId));\n      } catch (err) {\n        console.error('Error deleting user:', err);\n        alert('Failed to delete user');\n      }\n    }\n  };\n\n  const handleDeletePost = async (postId: string) => {\n    if (confirm('Are you sure you want to delete this post?')) {\n      try {\n        await apiClient.deletePost(postId);\n        setPosts(posts.filter(post => post.id !== postId));\n      } catch (err) {\n        console.error('Error deleting post:', err);\n        alert('Failed to delete post');\n      }\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <header className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Full-Stack App\n          </h1>\n          <p className=\"text-gray-600\">\n            Next.js + Express + Prisma + PostgreSQL\n          </p>\n        </header>\n\n        {/* Tab Navigation */}\n        <div className=\"mb-6\">\n          <nav className=\"flex space-x-8\">\n            <button\n              onClick={() => setActiveTab('users')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'users'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              Users ({users.length})\n            </button>\n            <button\n              onClick={() => setActiveTab('posts')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'posts'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              Posts ({posts.length})\n            </button>\n          </nav>\n        </div>\n\n        {/* Content */}\n        {loading ? (\n          <div className=\"flex justify-center items-center h-64\">\n            <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"></div>\n          </div>\n        ) : error ? (\n          <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n            <div className=\"flex\">\n              <div className=\"ml-3\">\n                <h3 className=\"text-sm font-medium text-red-800\">Error</h3>\n                <div className=\"mt-2 text-sm text-red-700\">\n                  <p>{error}</p>\n                </div>\n                <div className=\"mt-4\">\n                  <button\n                    onClick={fetchData}\n                    className=\"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\"\n                  >\n                    Try Again\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div>\n            {activeTab === 'users' && (\n              <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n                {users.length === 0 ? (\n                  <div className=\"col-span-full text-center py-12\">\n                    <p className=\"text-gray-500\">No users found</p>\n                  </div>\n                ) : (\n                  users.map((user) => (\n                    <UserCard\n                      key={user.id}\n                      user={user}\n                      onDelete={handleDeleteUser}\n                    />\n                  ))\n                )}\n              </div>\n            )}\n\n            {activeTab === 'posts' && (\n              <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n                {posts.length === 0 ? (\n                  <div className=\"col-span-full text-center py-12\">\n                    <p className=\"text-gray-500\">No posts found</p>\n                  </div>\n                ) : (\n                  posts.map((post) => (\n                    <PostCard\n                      key={post.id}\n                      post={post}\n                      onDelete={handleDeletePost}\n                    />\n                  ))\n                )}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAS,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAS,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAgB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAoB;IAE9D,IAAA,0KAAS;0BAAC;YACR;QACF;yBAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,CAAC,WAAW,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC/C,iIAAS,CAAC,QAAQ;gBAClB,iIAAS,CAAC,QAAQ;aACnB;YACD,SAAS;YACT,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,+CAA+C;YACzD,IAAI;gBACF,MAAM,iIAAS,CAAC,UAAU,CAAC;gBAC3B,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC5C,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM;YACR;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,+CAA+C;YACzD,IAAI;gBACF,MAAM,iIAAS,CAAC,UAAU,CAAC;gBAC3B,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC5C,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAM/B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,AAAC,4CAIX,OAHC,cAAc,UACV,kCACA;;oCAEP;oCACS,MAAM,MAAM;oCAAC;;;;;;;0CAEvB,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,AAAC,4CAIX,OAHC,cAAc,UACV,kCACA;;oCAEP;oCACS,MAAM,MAAM;oCAAC;;;;;;;;;;;;;;;;;;gBAM1B,wBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;2BAEf,sBACF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;kDAAG;;;;;;;;;;;8CAEN,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;yCAQT,6LAAC;;wBACE,cAAc,yBACb,6LAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;uCAG/B,MAAM,GAAG,CAAC,CAAC,qBACT,6LAAC,4IAAQ;oCAEP,MAAM;oCACN,UAAU;mCAFL,KAAK,EAAE;;;;;;;;;;wBASrB,cAAc,yBACb,6LAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;uCAG/B,MAAM,GAAG,CAAC,CAAC,qBACT,6LAAC,4IAAQ;oCAEP,MAAM;oCACN,UAAU;mCAFL,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAalC;GA7JwB;KAAA", "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, props, owner, debugStack, debugTask) {\n      var refProp = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== refProp ? refProp : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        maybeKey,\n        getOwner(),\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (type, config, maybeKey, isStaticChildren) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS;QAClE,IAAI,UAAU,MAAM,GAAG;QACvB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,UAAU,UAAU,IAAI,IACzC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,UACA,YACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB;QACjE,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}