{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/router-reducer/router-reducer-types.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport type { FetchServerResponseResult } from './fetch-server-response'\n\nexport const ACTION_REFRESH = 'refresh'\nexport const ACTION_NAVIGATE = 'navigate'\nexport const ACTION_RESTORE = 'restore'\nexport const ACTION_SERVER_PATCH = 'server-patch'\nexport const ACTION_PREFETCH = 'prefetch'\nexport const ACTION_HMR_REFRESH = 'hmr-refresh'\nexport const ACTION_SERVER_ACTION = 'server-action'\n\nexport type RouterChangeByServerResponse = ({\n  navigatedAt,\n  previousTree,\n  serverResponse,\n}: {\n  navigatedAt: number\n  previousTree: FlightRouterState\n  serverResponse: FetchServerResponseResult\n}) => void\n\nexport interface Mutable {\n  mpaNavigation?: boolean\n  patchedTree?: FlightRouterState\n  canonicalUrl?: string\n  scrollableSegments?: FlightSegmentPath[]\n  pendingPush?: boolean\n  cache?: CacheNode\n  prefetchCache?: AppRouterState['prefetchCache']\n  hashFragment?: string\n  shouldScroll?: boolean\n  preserveCustomHistoryState?: boolean\n  onlyHashChange?: boolean\n}\n\nexport interface ServerActionMutable extends Mutable {\n  inFlightServerAction?: Promise<any> | null\n}\n\n/**\n * Refresh triggers a refresh of the full page data.\n * - fetches the Flight data and fills rsc at the root of the cache.\n * - The router state is updated at the root.\n */\nexport interface RefreshAction {\n  type: typeof ACTION_REFRESH\n  origin: Location['origin']\n}\n\nexport interface HmrRefreshAction {\n  type: typeof ACTION_HMR_REFRESH\n  origin: Location['origin']\n}\n\nexport type ServerActionDispatcher = (\n  args: Omit<\n    ServerActionAction,\n    'type' | 'mutable' | 'navigate' | 'changeByServerResponse' | 'cache'\n  >\n) => void\n\nexport interface ServerActionAction {\n  type: typeof ACTION_SERVER_ACTION\n  actionId: string\n  actionArgs: any[]\n  resolve: (value: any) => void\n  reject: (reason?: any) => void\n}\n\n/**\n * Navigate triggers a navigation to the provided url. It supports two types: `push` and `replace`.\n *\n * `navigateType`:\n * - `push` - pushes a new history entry in the browser history\n * - `replace` - replaces the current history entry in the browser history\n *\n * Navigate has multiple cache heuristics:\n * - page was prefetched\n *  - Apply router state tree from prefetch\n *  - Apply Flight data from prefetch to the cache\n *  - If Flight data is a string, it's a redirect and the state is updated to trigger a redirect\n *  - Check if hard navigation is needed\n *    - Hard navigation happens when a dynamic parameter below the common layout changed\n *    - When hard navigation is needed the cache is invalidated below the flightSegmentPath\n *    - The missing cache nodes of the page will be fetched in layout-router and trigger the SERVER_PATCH action\n *  - If hard navigation is not needed\n *    - The cache is reused\n *    - If any cache nodes are missing they'll be fetched in layout-router and trigger the SERVER_PATCH action\n * - page was not prefetched\n *  - The navigate was called from `next/router` (`router.push()` / `router.replace()`) / `next/link` without prefetched data available (e.g. the prefetch didn't come back from the server before clicking the link)\n *    - Flight data is fetched in the reducer (suspends the reducer)\n *    - Router state tree is created based on Flight data\n *    - Cache is filled based on the Flight data\n *\n * Above steps explain 3 cases:\n * - `soft` - Reuses the existing cache and fetches missing nodes in layout-router.\n * - `hard` - Creates a new cache where cache nodes are removed below the common layout and fetches missing nodes in layout-router.\n * - `optimistic` (explicit no prefetch) - Creates a new cache and kicks off the data fetch in the reducer. The data fetch is awaited in the layout-router.\n */\nexport interface NavigateAction {\n  type: typeof ACTION_NAVIGATE\n  url: URL\n  isExternalUrl: boolean\n  locationSearch: Location['search']\n  navigateType: 'push' | 'replace'\n  shouldScroll: boolean\n  allowAliasing: boolean\n}\n\n/**\n * Restore applies the provided router state.\n * - Used for `popstate` (back/forward navigation) where a known router state has to be applied.\n * - Also used when syncing the router state with `pushState`/`replaceState` calls.\n * - Router state is applied as-is from the history state, if available.\n * - If the history state does not contain the router state, the existing router state is used.\n * - If any cache node is missing it will be fetched in layout-router during rendering and the server-patch case.\n * - If existing cache nodes match these are used.\n */\nexport interface RestoreAction {\n  type: typeof ACTION_RESTORE\n  url: URL\n  tree: FlightRouterState | undefined\n}\n\n/**\n * Server-patch applies the provided Flight data to the cache and router tree.\n * - Only triggered in layout-router.\n * - Creates a new cache and router state with the Flight data applied.\n */\nexport interface ServerPatchAction {\n  type: typeof ACTION_SERVER_PATCH\n  navigatedAt: number\n  serverResponse: FetchServerResponseResult\n  previousTree: FlightRouterState\n}\n\n/**\n * PrefetchKind defines the type of prefetching that should be done.\n * - `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully.\n * - `full` - prefetch the page data fully.\n * - `temporary` - a temporary prefetch entry is added to the cache, this is used when prefetch={false} is used in next/link or when you push a route programmatically.\n */\n\nexport enum PrefetchKind {\n  AUTO = 'auto',\n  FULL = 'full',\n  TEMPORARY = 'temporary',\n}\n\n/**\n * Prefetch adds the provided FlightData to the prefetch cache\n * - Creates the router state tree based on the patch in FlightData\n * - Adds the FlightData to the prefetch cache\n * - In ACTION_NAVIGATE the prefetch cache is checked and the router state tree and FlightData are applied.\n */\nexport interface PrefetchAction {\n  type: typeof ACTION_PREFETCH\n  url: URL\n  kind: PrefetchKind\n}\n\nexport interface PushRef {\n  /**\n   * If the app-router should push a new history entry in app-router's useEffect()\n   */\n  pendingPush: boolean\n  /**\n   * Multi-page navigation through location.href.\n   */\n  mpaNavigation: boolean\n  /**\n   * Skip applying the router state to the browser history state.\n   */\n  preserveCustomHistoryState: boolean\n}\n\nexport type FocusAndScrollRef = {\n  /**\n   * If focus and scroll should be set in the layout-router's useEffect()\n   */\n  apply: boolean\n  /**\n   * The hash fragment that should be scrolled to.\n   */\n  hashFragment: string | null\n  /**\n   * The paths of the segments that should be focused.\n   */\n  segmentPaths: FlightSegmentPath[]\n  /**\n   * If only the URLs hash fragment changed\n   */\n  onlyHashChange: boolean\n}\n\nexport type PrefetchCacheEntry = {\n  treeAtTimeOfPrefetch: FlightRouterState\n  data: Promise<FetchServerResponseResult>\n  kind: PrefetchKind\n  prefetchTime: number\n  staleTime: number\n  lastUsedTime: number | null\n  key: string\n  status: PrefetchCacheEntryStatus\n  url: URL\n}\n\nexport enum PrefetchCacheEntryStatus {\n  fresh = 'fresh',\n  reusable = 'reusable',\n  expired = 'expired',\n  stale = 'stale',\n}\n\n/**\n * Handles keeping the state of app-router.\n */\nexport type AppRouterState = {\n  /**\n   * The router state, this is written into the history state in app-router using replaceState/pushState.\n   * - Has to be serializable as it is written into the history state.\n   * - Holds which segments and parallel routes are shown on the screen.\n   */\n  tree: FlightRouterState\n  /**\n   * The cache holds React nodes for every segment that is shown on screen as well as previously shown segments.\n   * It also holds in-progress data requests.\n   * Prefetched data is stored separately in `prefetchCache`, that is applied during ACTION_NAVIGATE.\n   */\n  cache: CacheNode\n  /**\n   * Cache that holds prefetched Flight responses keyed by url.\n   */\n  prefetchCache: Map<string, PrefetchCacheEntry>\n  /**\n   * Decides if the update should create a new history entry and if the navigation has to trigger a browser navigation.\n   */\n  pushRef: PushRef\n  /**\n   * Decides if the update should apply scroll and focus management.\n   */\n  focusAndScrollRef: FocusAndScrollRef\n  /**\n   * The canonical url that is pushed/replaced.\n   * - This is the url you see in the browser.\n   */\n  canonicalUrl: string\n  /**\n   * The underlying \"url\" representing the UI state, which is used for intercepting routes.\n   */\n  nextUrl: string | null\n}\n\nexport type ReadonlyReducerState = Readonly<AppRouterState>\nexport type ReducerState = Promise<AppRouterState> | AppRouterState\nexport type ReducerActions = Readonly<\n  | RefreshAction\n  | NavigateAction\n  | RestoreAction\n  | ServerPatchAction\n  | PrefetchAction\n  | HmrRefreshAction\n  | ServerActionAction\n>\n"], "names": ["ACTION_REFRESH", "ACTION_NAVIGATE", "ACTION_RESTORE", "ACTION_SERVER_PATCH", "ACTION_PREFETCH", "ACTION_HMR_REFRESH", "ACTION_SERVER_ACTION", "PrefetchKind", "PrefetchCacheEntryStatus"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAOO,MAAMA,iBAAiB,UAAS;AAChC,MAAMC,kBAAkB,WAAU;AAClC,MAAMC,iBAAiB,UAAS;AAChC,MAAMC,sBAAsB,eAAc;AAC1C,MAAMC,kBAAkB,WAAU;AAClC,MAAMC,qBAAqB,cAAa;AACxC,MAAMC,uBAAuB,gBAAe;AAsI5C,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;WAAAA;MAIX;AA4DM,IAAKC,2BAAAA,WAAAA,GAAAA,SAAAA,wBAAAA;;;;;WAAAA;MAKX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'rsc' as const\nexport const ACTION_HEADER = 'next-action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'next-router-state-tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'next-router-prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change next-router-state-tree to be a segment path, we can use\n// that instead. Then next-router-prefetch and next-router-segment-prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'next-router-segment-prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'next-hmr-refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'next-url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\nexport const NEXT_ACTION_NOT_FOUND_HEADER = 'x-nextjs-action-not-found' as const\n"], "names": ["RSC_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "FLIGHT_HEADERS", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_ACTION_NOT_FOUND_HEADER"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAMA,aAAa,MAAc;AACjC,MAAMC,gBAAgB,cAAsB;AAI5C,MAAMC,gCAAgC,yBAAiC;AACvE,MAAMC,8BAA8B,uBAA+B;AAKnE,MAAMC,sCACX,+BAAuC;AAClC,MAAMC,0BAA0B,mBAA2B;AAC3D,MAAMC,+BAA+B,4BAAoC;AACzE,MAAMC,WAAW,WAAmB;AACpC,MAAMC,0BAA0B,mBAA2B;AAE3D,MAAMC,iBAAiB;IAC5BT;IACAE;IACAC;IACAE;IACAD;CACD,CAAS;AAEH,MAAMM,uBAAuB,OAAe;AAE5C,MAAMC,gCAAgC,sBAA8B;AACpE,MAAMC,2BAA2B,qBAA6B;AAC9D,MAAMC,6BAA6B,0BAAkC;AACrE,MAAMC,8BAA8B,2BAAmC;AACvE,MAAMC,2BAA2B,qBAA6B;AAC9D,MAAMC,+BAA+B,4BAAoC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/is-thenable.ts"], "sourcesContent": ["/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n"], "names": ["isThenable", "promise", "then"], "mappings": "AAAA;;;;;CAKC,GACD;;;;AAAO,SAASA,WACdC,OAAuB;IAEvB,OACEA,YAAY,QACZ,OAAOA,YAAY,YACnB,UAAUA,WACV,OAAOA,QAAQC,IAAI,KAAK;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/next-devtools/userspace/use-app-dev-rendering-indicator.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useTransition } from 'react'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\n\nexport const useAppDevRenderingIndicator = () => {\n  const [isPending, startTransition] = useTransition()\n\n  useEffect(() => {\n    if (isPending) {\n      dispatcher.renderingIndicatorShow()\n    } else {\n      dispatcher.renderingIndicatorHide()\n    }\n  }, [isPending])\n\n  return startTransition\n}\n"], "names": ["useEffect", "useTransition", "dispatcher", "useAppDevRenderingIndicator", "isPending", "startTransition", "renderingIndicatorShow", "renderingIndicatorHide"], "mappings": ";;;;AAEA,SAASA,SAAS,EAAEC,aAAa,QAAQ,QAAO;AAChD,SAASC,UAAU,QAAQ,mCAAkC;AAH7D;;;AAKO,MAAMC,8BAA8B;IACzC,MAAM,CAACC,WAAWC,gBAAgB,OAAGJ,sNAAAA;QAErCD,kNAAAA,EAAU;QACR,IAAII,WAAW;YACbF,mLAAAA,CAAWI,sBAAsB;QACnC,OAAO;YACLJ,mLAAAA,CAAWK,sBAAsB;QACnC;IACF,GAAG;QAACH;KAAU;IAEd,OAAOC;AACT,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/use-action-queue.ts"], "sourcesContent": ["import type { Dispatch } from 'react'\nimport React, { use } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport type { AppRouterActionQueue } from './app-router-instance'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\n\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch: Dispatch<ReducerActions> | null = null\n\nexport function dispatchAppRouterAction(action: ReducerActions) {\n  if (dispatch === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  dispatch(action)\n}\n\nexport function useActionQueue(\n  actionQueue: AppRouterActionQueue\n): AppRouterState {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n\n  // Because of a known issue that requires to decode Flight streams inside the\n  // render phase, we have to be a bit clever and assign the dispatch method to\n  // a module-level variable upon initialization. The useState hook in this\n  // module only exists to synchronize state that lives outside of React.\n  // Ideally, what we'd do instead is pass the state as a prop to root.render;\n  // this is conceptually how we're modeling the app router state, despite the\n  // weird implementation details.\n  if (process.env.NODE_ENV !== 'production') {\n    const { useAppDevRenderingIndicator } =\n      require('../../next-devtools/userspace/use-app-dev-rendering-indicator') as typeof import('../../next-devtools/userspace/use-app-dev-rendering-indicator')\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const appDevRenderingIndicator = useAppDevRenderingIndicator()\n\n    dispatch = (action: ReducerActions) => {\n      appDevRenderingIndicator(() => {\n        actionQueue.dispatch(action, setState)\n      })\n    }\n  } else {\n    dispatch = (action: ReducerActions) =>\n      actionQueue.dispatch(action, setState)\n  }\n\n  return isThenable(state) ? use(state) : state\n}\n"], "names": ["React", "use", "isThenable", "dispatch", "dispatchAppRouterAction", "action", "Error", "useActionQueue", "actionQueue", "state", "setState", "useState", "process", "env", "NODE_ENV", "useAppDevRenderingIndicator", "require", "appDevRenderingIndicator"], "mappings": ";;;;;;AACA,OAAOA,SAASC,GAAG,QAAQ,QAAO;AAClC,SAASC,UAAU,QAAQ,+BAA8B;;;AAQzD,6EAA6E;AAC7E,+EAA+E;AAC/E,cAAc;AACd,IAAIC,WAA4C;AAEzC,SAASC,wBAAwBC,MAAsB;IAC5D,IAAIF,aAAa,MAAM;QACrB,MAAM,OAAA,cAEL,CAFK,IAAIG,MACR,4EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAH,SAASE;AACX;AAEO,SAASE,eACdC,WAAiC;IAEjC,MAAM,CAACC,OAAOC,SAAS,GAAGV,gNAAAA,CAAMW,QAAQ,CAAeH,YAAYC,KAAK;IAExE,6EAA6E;IAC7E,6EAA6E;IAC7E,yEAAyE;IACzE,uEAAuE;IACvE,4EAA4E;IAC5E,4EAA4E;IAC5E,gCAAgC;IAChC,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,MAAM,EAAEC,2BAA2B,EAAE,GACnCC,QAAQ;QACV,sDAAsD;QACtD,MAAMC,2BAA2BF;QAEjCZ,WAAW,CAACE;YACVY,yBAAyB;gBACvBT,YAAYL,QAAQ,CAACE,QAAQK;YAC/B;QACF;IACF,OAAO;;IAKP,WAAOR,oLAAAA,EAAWO,aAASR,4MAAAA,EAAIQ,SAASA;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/app-call-server.ts"], "sourcesContent": ["import { startTransition } from 'react'\nimport { ACTION_SERVER_ACTION } from './components/router-reducer/router-reducer-types'\nimport { dispatchAppRouterAction } from './components/use-action-queue'\n\nexport async function callServer(actionId: string, actionArgs: any[]) {\n  return new Promise((resolve, reject) => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_SERVER_ACTION,\n        actionId,\n        actionArgs,\n        resolve,\n        reject,\n      })\n    })\n  })\n}\n"], "names": ["startTransition", "ACTION_SERVER_ACTION", "dispatchAppRouterAction", "callServer", "actionId", "actionArgs", "Promise", "resolve", "reject", "type"], "mappings": ";;;;AAAA,SAASA,eAAe,QAAQ,QAAO;AACvC,SAASC,oBAAoB,QAAQ,mDAAkD;AACvF,SAASC,uBAAuB,QAAQ,gCAA+B;;;;AAEhE,eAAeC,WAAWC,QAAgB,EAAEC,UAAiB;IAClE,OAAO,IAAIC,QAAQ,CAACC,SAASC;YAC3BR,wNAAAA,EAAgB;gBACdE,gNAAAA,EAAwB;gBACtBO,MAAMR,sOAAAA;gBACNG;gBACAC;gBACAE;gBACAC;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/app-find-source-map-url.ts"], "sourcesContent": ["const basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\nconst pathname = `${basePath}/__nextjs_source-map`\n\nexport const findSourceMapURL =\n  process.env.NODE_ENV === 'development'\n    ? function findSourceMapURL(filename: string): string | null {\n        if (filename === '') {\n          return null\n        }\n\n        if (\n          filename.startsWith(document.location.origin) &&\n          filename.includes('/_next/static')\n        ) {\n          // This is a request for a client chunk. This can only happen when\n          // using Turbopack. In this case, since we control how those source\n          // maps are generated, we can safely assume that the sourceMappingURL\n          // is relative to the filename, with an added `.map` extension. The\n          // browser can just request this file, and it gets served through the\n          // normal dev server, without the need to route this through\n          // the `/__nextjs_source-map` dev middleware.\n          return `${filename}.map`\n        }\n\n        const url = new URL(pathname, document.location.origin)\n        url.searchParams.set('filename', filename)\n\n        return url.href\n      }\n    : undefined\n"], "names": ["basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "pathname", "findSourceMapURL", "NODE_ENV", "filename", "startsWith", "document", "location", "origin", "includes", "url", "URL", "searchParams", "set", "href", "undefined"], "mappings": ";;;;AAAA,MAAMA,WAAWC,QAAQC,GAAG,CAACC,sBAAsB,MAAI;AACvD,MAAMC,WAAY,KAAEJ,WAAS;AAEtB,MAAMK,mBACXJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,cACrB,SAASD,iBAAiBE,QAAgB;IACxC,IAAIA,aAAa,IAAI;QACnB,OAAO;IACT;IAEA,IACEA,SAASC,UAAU,CAACC,SAASC,QAAQ,CAACC,MAAM,KAC5CJ,SAASK,QAAQ,CAAC,kBAClB;QACA,kEAAkE;QAClE,mEAAmE;QACnE,qEAAqE;QACrE,mEAAmE;QACnE,qEAAqE;QACrE,4DAA4D;QAC5D,6CAA6C;QAC7C,OAAQ,KAAEL,WAAS;IACrB;IAEA,MAAMM,MAAM,IAAIC,IAAIV,UAAUK,SAASC,QAAQ,CAACC,MAAM;IACtDE,IAAIE,YAAY,CAACC,GAAG,CAAC,YAAYT;IAEjC,OAAOM,IAAII,IAAI;AACjB,IACAC,UAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["isGroupSegment", "segment", "endsWith", "isParallelRouteSegment", "startsWith", "addSearchParamsIfPageSegment", "searchParams", "isPageSegment", "includes", "PAGE_SEGMENT_KEY", "stringified<PERSON><PERSON>y", "JSON", "stringify", "DEFAULT_SEGMENT_KEY"], "mappings": ";;;;;;;;;;;;AAEO,SAASA,eAAeC,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASC,uBAAuBF,OAAe;IACpD,OAAOA,QAAQG,UAAU,CAAC,QAAQH,YAAY;AAChD;AAEO,SAASI,6BACdJ,OAAgB,EAChBK,YAA2D;IAE3D,MAAMC,gBAAgBN,QAAQO,QAAQ,CAACC;IAEvC,IAAIF,eAAe;QACjB,MAAMG,mBAAmBC,KAAKC,SAAS,CAACN;QACxC,OAAOI,qBAAqB,OACxBD,mBAAmB,MAAMC,mBACzBD;IACN;IAEA,OAAOR;AACT;AAEO,MAAMQ,mBAAmB,WAAU;AACnC,MAAMI,sBAAsB,cAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/flight-data-helpers.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightData,\n  FlightDataPath,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../server/app-render/types'\nimport type { HeadData } from '../shared/lib/app-router-context.shared-runtime'\nimport { PAGE_SEGMENT_KEY } from '../shared/lib/segment'\n\nexport type NormalizedFlightData = {\n  /**\n   * The full `FlightSegmentPath` inclusive of the final `Segment`\n   */\n  segmentPath: FlightSegmentPath\n  /**\n   * The `FlightSegmentPath` exclusive of the final `Segment`\n   */\n  pathToSegment: FlightSegmentPath\n  segment: Segment\n  tree: FlightRouterState\n  seedData: CacheNodeSeedData | null\n  head: HeadData\n  isHeadPartial: boolean\n  isRootRender: boolean\n}\n\n// TODO: We should only have to export `normalizeFlightData`, however because the initial flight data\n// that gets passed to `createInitialRouterState` doesn't conform to the `FlightDataPath` type (it's missing the root segment)\n// we're currently exporting it so we can use it directly. This should be fixed as part of the unification of\n// the different ways we express `FlightSegmentPath`.\nexport function getFlightDataPartsFromPath(\n  flightDataPath: FlightDataPath\n): NormalizedFlightData {\n  // Pick the last 4 items from the `FlightDataPath` to get the [tree, seedData, viewport, isHeadPartial].\n  const flightDataPathLength = 4\n  // tree, seedData, and head are *always* the last three items in the `FlightDataPath`.\n  const [tree, seedData, head, isHeadPartial] =\n    flightDataPath.slice(-flightDataPathLength)\n  // The `FlightSegmentPath` is everything except the last three items. For a root render, it won't be present.\n  const segmentPath = flightDataPath.slice(0, -flightDataPathLength)\n\n  return {\n    // TODO: Unify these two segment path helpers. We are inconsistently pushing an empty segment (\"\")\n    // to the start of the segment path in some places which makes it hard to use solely the segment path.\n    // Look for \"// TODO-APP: remove ''\" in the codebase.\n    pathToSegment: segmentPath.slice(0, -1),\n    segmentPath,\n    // if the `FlightDataPath` corresponds with the root, there'll be no segment path,\n    // in which case we default to ''.\n    segment: segmentPath[segmentPath.length - 1] ?? '',\n    tree,\n    seedData,\n    head,\n    isHeadPartial,\n    isRootRender: flightDataPath.length === flightDataPathLength,\n  }\n}\n\nexport function getNextFlightSegmentPath(\n  flightSegmentPath: FlightSegmentPath\n): FlightSegmentPath {\n  // Since `FlightSegmentPath` is a repeated tuple of `Segment` and `ParallelRouteKey`, we slice off two items\n  // to get the next segment path.\n  return flightSegmentPath.slice(2)\n}\n\nexport function normalizeFlightData(\n  flightData: FlightData\n): NormalizedFlightData[] | string {\n  // FlightData can be a string when the server didn't respond with a proper flight response,\n  // or when a redirect happens, to signal to the client that it needs to perform an MPA navigation.\n  if (typeof flightData === 'string') {\n    return flightData\n  }\n\n  return flightData.map((flightDataPath) =>\n    getFlightDataPartsFromPath(flightDataPath)\n  )\n}\n\n/**\n * This function is used to prepare the flight router state for the request.\n * It removes markers that are not needed by the server, and are purely used\n * for stashing state on the client.\n * @param flightRouterState - The flight router state to prepare.\n * @param isHmrRefresh - Whether this is an HMR refresh request.\n * @returns The prepared flight router state.\n */\nexport function prepareFlightRouterStateForRequest(\n  flightRouterState: FlightRouterState,\n  isHmrRefresh?: boolean\n): string {\n  // HMR requests need the complete, unmodified state for proper functionality\n  if (isHmrRefresh) {\n    return encodeURIComponent(JSON.stringify(flightRouterState))\n  }\n\n  return encodeURIComponent(\n    JSON.stringify(stripClientOnlyDataFromFlightRouterState(flightRouterState))\n  )\n}\n\n/**\n * Recursively strips client-only data from FlightRouterState while preserving\n * server-needed information for proper rendering decisions.\n */\nfunction stripClientOnlyDataFromFlightRouterState(\n  flightRouterState: FlightRouterState\n): FlightRouterState {\n  const [\n    segment,\n    parallelRoutes,\n    _url, // Intentionally unused - URLs are client-only\n    refreshMarker,\n    isRootLayout,\n    hasLoadingBoundary,\n  ] = flightRouterState\n\n  // __PAGE__ segments are always fetched from the server, so there's\n  // no need to send them up\n  const cleanedSegment = stripSearchParamsFromPageSegment(segment)\n\n  // Recursively process parallel routes\n  const cleanedParallelRoutes: { [key: string]: FlightRouterState } = {}\n  for (const [key, childState] of Object.entries(parallelRoutes)) {\n    cleanedParallelRoutes[key] =\n      stripClientOnlyDataFromFlightRouterState(childState)\n  }\n\n  const result: FlightRouterState = [\n    cleanedSegment,\n    cleanedParallelRoutes,\n    null, // URLs omitted - server reconstructs paths from segments\n    shouldPreserveRefreshMarker(refreshMarker) ? refreshMarker : null,\n  ]\n\n  // Append optional fields if present\n  if (isRootLayout !== undefined) {\n    result[4] = isRootLayout\n  }\n  if (hasLoadingBoundary !== undefined) {\n    result[5] = hasLoadingBoundary\n  }\n\n  return result\n}\n\n/**\n * Strips search parameters from __PAGE__ segments to prevent sensitive\n * client-side data from being sent to the server.\n */\nfunction stripSearchParamsFromPageSegment(segment: Segment): Segment {\n  if (\n    typeof segment === 'string' &&\n    segment.startsWith(PAGE_SEGMENT_KEY + '?')\n  ) {\n    return PAGE_SEGMENT_KEY\n  }\n  return segment\n}\n\n/**\n * Determines whether the refresh marker should be sent to the server\n * Client-only markers like 'refresh' are stripped, while server-needed markers\n * like 'refetch' and 'inside-shared-layout' are preserved.\n */\nfunction shouldPreserveRefreshMarker(\n  refreshMarker: FlightRouterState[3]\n): boolean {\n  return Boolean(refreshMarker && refreshMarker !== 'refresh')\n}\n"], "names": ["PAGE_SEGMENT_KEY", "getFlightDataPartsFromPath", "flightDataPath", "flightDataPathLength", "tree", "seedData", "head", "isHeadPartial", "slice", "segmentPath", "pathToSegment", "segment", "length", "isRootRender", "getNextFlightSegmentPath", "flightSegmentPath", "normalizeFlightData", "flightData", "map", "prepareFlightRouterStateForRequest", "flightRouterState", "isHmrRefresh", "encodeURIComponent", "JSON", "stringify", "stripClientOnlyDataFromFlightRouterState", "parallelRoutes", "_url", "refresh<PERSON><PERSON><PERSON>", "isRootLayout", "hasLoadingBoundary", "cleanedSegment", "stripSearchParamsFromPageSegment", "cleanedParallelRoutes", "key", "childState", "Object", "entries", "result", "shouldPreserveRefreshMarker", "undefined", "startsWith", "Boolean"], "mappings": ";;;;;;;;;;AASA,SAASA,gBAAgB,QAAQ,wBAAuB;;AAuBjD,SAASC,2BACdC,cAA8B;IAE9B,wGAAwG;IACxG,MAAMC,uBAAuB;IAC7B,sFAAsF;IACtF,MAAM,CAACC,MAAMC,UAAUC,MAAMC,cAAc,GACzCL,eAAeM,KAAK,CAAC,CAACL;IACxB,6GAA6G;IAC7G,MAAMM,cAAcP,eAAeM,KAAK,CAAC,GAAG,CAACL;QAUlCM;IARX,OAAO;QACL,kGAAkG;QAClG,sGAAsG;QACtG,qDAAqD;QACrDC,eAAeD,YAAYD,KAAK,CAAC,GAAG,CAAC;QACrCC;QACA,kFAAkF;QAClF,kCAAkC;QAClCE,SAASF,CAAAA,gBAAAA,WAAW,CAACA,YAAYG,MAAM,GAAG,EAAE,KAAA,OAAnCH,gBAAuC;QAChDL;QACAC;QACAC;QACAC;QACAM,cAAcX,eAAeU,MAAM,KAAKT;IAC1C;AACF;AAEO,SAASW,yBACdC,iBAAoC;IAEpC,4GAA4G;IAC5G,gCAAgC;IAChC,OAAOA,kBAAkBP,KAAK,CAAC;AACjC;AAEO,SAASQ,oBACdC,UAAsB;IAEtB,2FAA2F;IAC3F,kGAAkG;IAClG,IAAI,OAAOA,eAAe,UAAU;QAClC,OAAOA;IACT;IAEA,OAAOA,WAAWC,GAAG,CAAC,CAAChB,iBACrBD,2BAA2BC;AAE/B;AAUO,SAASiB,mCACdC,iBAAoC,EACpCC,YAAsB;IAEtB,4EAA4E;IAC5E,IAAIA,cAAc;QAChB,OAAOC,mBAAmBC,KAAKC,SAAS,CAACJ;IAC3C;IAEA,OAAOE,mBACLC,KAAKC,SAAS,CAACC,yCAAyCL;AAE5D;AAEA;;;CAGC,GACD,SAASK,yCACPL,iBAAoC;IAEpC,MAAM,CACJT,SACAe,gBACAC,MACAC,eACAC,cACAC,mBACD,GAAGV;IAEJ,mEAAmE;IACnE,0BAA0B;IAC1B,MAAMW,iBAAiBC,iCAAiCrB;IAExD,sCAAsC;IACtC,MAAMsB,wBAA8D,CAAC;IACrE,KAAK,MAAM,CAACC,KAAKC,WAAW,IAAIC,OAAOC,OAAO,CAACX,gBAAiB;QAC9DO,qBAAqB,CAACC,IAAI,GACxBT,yCAAyCU;IAC7C;IAEA,MAAMG,SAA4B;QAChCP;QACAE;QACA;QACAM,4BAA4BX,iBAAiBA,gBAAgB;KAC9D;IAED,oCAAoC;IACpC,IAAIC,iBAAiBW,WAAW;QAC9BF,MAAM,CAAC,EAAE,GAAGT;IACd;IACA,IAAIC,uBAAuBU,WAAW;QACpCF,MAAM,CAAC,EAAE,GAAGR;IACd;IAEA,OAAOQ;AACT;AAEA;;;CAGC,GACD,SAASN,iCAAiCrB,OAAgB;IACxD,IACE,OAAOA,YAAY,YACnBA,QAAQ8B,UAAU,CAACzC,mLAAAA,GAAmB,MACtC;QACA,OAAOA,mLAAAA;IACT;IACA,OAAOW;AACT;AAEA;;;;CAIC,GACD,SAAS4B,4BACPX,aAAmC;IAEnC,OAAOc,QAAQd,iBAAiBA,kBAAkB;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/app-build-id.ts"], "sourcesContent": ["// This gets assigned as a side-effect during app initialization. Because it\n// represents the build used to create the JS bundle, it should never change\n// after being set, so we store it in a global variable.\n//\n// When performing RSC requests, if the incoming data has a different build ID,\n// we perform an MPA navigation/refresh to load the updated build and ensure\n// that the client and server in sync.\n\n// Starts as an empty string. In practice, because setAppBuildId is called\n// during initialization before hydration starts, this will always get\n// reassigned to the actual build ID before it's ever needed by a navigation.\n// If for some reasons it didn't, due to a bug or race condition, then on\n// navigation the build comparision would fail and trigger an MPA navigation.\nlet globalBuildId: string = ''\n\nexport function setAppBuildId(buildId: string) {\n  globalBuildId = buildId\n}\n\nexport function getAppBuildId(): string {\n  return globalBuildId\n}\n"], "names": ["globalBuildId", "setAppBuildId", "buildId", "getAppBuildId"], "mappings": "AAAA,4EAA4E;AAC5E,4EAA4E;AAC5E,wDAAwD;AACxD,EAAE;AACF,+EAA+E;AAC/E,4EAA4E;AAC5E,sCAAsC;AAEtC,0EAA0E;AAC1E,sEAAsE;AACtE,6EAA6E;AAC7E,yEAAyE;AACzE,6EAA6E;;;;;;;AAC7E,IAAIA,gBAAwB;AAErB,SAASC,cAAcC,OAAe;IAC3CF,gBAAgBE;AAClB;AAEO,SAASC;IACd,OAAOH;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/hash.ts"], "sourcesContent": ["// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\nexport function djb2Hash(str: string) {\n  let hash = 5381\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i)\n    hash = ((hash << 5) + hash + char) & 0xffffffff\n  }\n  return hash >>> 0\n}\n\nexport function hexHash(str: string) {\n  return djb2Hash(str).toString(36).slice(0, 5)\n}\n"], "names": ["djb2Hash", "str", "hash", "i", "length", "char", "charCodeAt", "hexHash", "toString", "slice"], "mappings": "AAAA,wCAAwC;AACxC,4CAA4C;AAC5C,iHAAiH;AACjH,wFAAwF;AACxF,gGAAgG;AAChG,wHAAwH;AACxH,wDAAwD;;;;;;;AACjD,SAASA,SAASC,GAAW;IAClC,IAAIC,OAAO;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,MAAME,OAAOJ,IAAIK,UAAU,CAACH;QAC5BD,OAASA,CAAAA,QAAQ,CAAA,IAAKA,OAAOG,OAAQ;IACvC;IACA,OAAOH,SAAS;AAClB;AAEO,SAASK,QAAQN,GAAW;IACjC,OAAOD,SAASC,KAAKO,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/router/utils/cache-busting-search-param.ts"], "sourcesContent": ["import { hexHash } from '../../hash'\n\nexport function computeCacheBustingSearchParam(\n  prefetchHeader: '1' | '2' | '0' | undefined,\n  segmentPrefetchHeader: string | string[] | undefined,\n  stateTreeHeader: string | string[] | undefined,\n  nextUrlHeader: string | string[] | undefined\n): string {\n  if (\n    (prefetchHeader === undefined || prefetchHeader === '0') &&\n    segmentPrefetchHeader === undefined &&\n    stateTreeHeader === undefined &&\n    nextUrlHeader === undefined\n  ) {\n    return ''\n  }\n  return hexHash(\n    [\n      prefetchHeader || '0',\n      segmentPrefetchHeader || '0',\n      stateTreeHeader || '0',\n      nextUrlHeader || '0',\n    ].join(',')\n  )\n}\n"], "names": ["hexHash", "computeCacheBustingSearchParam", "prefetch<PERSON><PERSON><PERSON>", "segmentPrefetchHeader", "stateTreeHeader", "nextUrl<PERSON><PERSON>er", "undefined", "join"], "mappings": ";;;;AAAA,SAASA,OAAO,QAAQ,aAAY;;AAE7B,SAASC,+BACdC,cAA2C,EAC3CC,qBAAoD,EACpDC,eAA8C,EAC9CC,aAA4C;IAE5C,IACGH,CAAAA,mBAAmBI,aAAaJ,mBAAmB,GAAE,KACtDC,0BAA0BG,aAC1BF,oBAAoBE,aACpBD,kBAAkBC,WAClB;QACA,OAAO;IACT;IACA,WAAON,uKAAAA,EACL;QACEE,kBAAkB;QAClBC,yBAAyB;QACzBC,mBAAmB;QACnBC,iBAAiB;KAClB,CAACE,IAAI,CAAC;AAEX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/router-reducer/set-cache-busting-search-param.ts"], "sourcesContent": ["'use client'\n\nimport { computeCacheBustingSearchParam } from '../../../shared/lib/router/utils/cache-busting-search-param'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  NEXT_RSC_UNION_QUERY,\n} from '../app-router-headers'\nimport type { RequestHeaders } from './fetch-server-response'\n\n/**\n * Mutates the provided URL by adding a cache-busting search parameter for CDNs that don't\n * support custom headers. This helps avoid caching conflicts by making each request unique.\n *\n * Rather than relying on the Vary header which some CDNs ignore, we append a search param\n * to create a unique URL that forces a fresh request.\n *\n * Example:\n * URL before: https://example.com/path?query=1\n * URL after: https://example.com/path?query=1&_rsc=abc123\n *\n * Note: This function mutates the input URL directly and does not return anything.\n *\n * TODO: Since we need to use a search param anyway, we could simplify by removing the custom\n * headers approach entirely and just use search params.\n */\nexport const setCacheBustingSearchParam = (\n  url: URL,\n  headers: RequestHeaders\n): void => {\n  const uniqueCacheKey = computeCacheBustingSearchParam(\n    headers[NEXT_ROUTER_PREFETCH_HEADER],\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER],\n    headers[NEXT_ROUTER_STATE_TREE_HEADER],\n    headers[NEXT_URL]\n  )\n  setCacheBustingSearchParamWithHash(url, uniqueCacheKey)\n}\n\n/**\n * Sets a cache-busting search parameter on a URL using a provided hash value.\n *\n * This function performs the same logic as `setCacheBustingSearchParam` but accepts\n * a pre-computed hash instead of computing it from headers.\n *\n * Example:\n * URL before: https://example.com/path?query=1\n * hash: \"abc123\"\n * URL after: https://example.com/path?query=1&_rsc=abc123\n *\n * If the hash is null, we will set `_rsc` search param without a value.\n * Like this: https://example.com/path?query=1&_rsc\n *\n * Note: This function mutates the input URL directly and does not return anything.\n */\nexport const setCacheBustingSearchParamWithHash = (\n  url: URL,\n  hash: string\n): void => {\n  /**\n   * Note that we intentionally do not use `url.searchParams.set` here:\n   *\n   * const url = new URL('https://example.com/search?q=custom%20spacing');\n   * url.searchParams.set('_rsc', 'abc123');\n   * console.log(url.toString()); // Outputs: https://example.com/search?q=custom+spacing&_rsc=abc123\n   *                                                                             ^ <--- this is causing confusion\n   * This is in fact intended based on https://url.spec.whatwg.org/#interface-urlsearchparams, but\n   * we want to preserve the %20 as %20 if that's what the user passed in, hence the custom\n   * logic below.\n   */\n  const existingSearch = url.search\n  const rawQuery = existingSearch.startsWith('?')\n    ? existingSearch.slice(1)\n    : existingSearch\n\n  // Always remove any existing cache busting param and add a fresh one to ensure\n  // we have the correct value based on current request headers\n  const pairs = rawQuery\n    .split('&')\n    .filter((pair) => pair && !pair.startsWith(`${NEXT_RSC_UNION_QUERY}=`))\n\n  if (hash.length > 0) {\n    pairs.push(`${NEXT_RSC_UNION_QUERY}=${hash}`)\n  } else {\n    pairs.push(`${NEXT_RSC_UNION_QUERY}`)\n  }\n  url.search = pairs.length ? `?${pairs.join('&')}` : ''\n}\n"], "names": ["computeCacheBustingSearchParam", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_URL", "NEXT_RSC_UNION_QUERY", "setCacheBustingSearchParam", "url", "headers", "unique<PERSON><PERSON><PERSON><PERSON>", "setCacheBustingSearchParamWithHash", "hash", "existingSearch", "search", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "slice", "pairs", "split", "filter", "pair", "length", "push", "join"], "mappings": ";;;;;;AAEA,SAASA,8BAA8B,QAAQ,8DAA6D;AAC5G,SACEC,2BAA2B,EAC3BC,mCAAmC,EACnCC,6BAA6B,EAC7BC,QAAQ,EACRC,oBAAoB,QACf,wBAAuB;AAT9B;;;AA4BO,MAAMC,6BAA6B,CACxCC,KACAC;IAEA,MAAMC,qBAAiBT,gPAAAA,EACrBQ,OAAO,CAACP,sNAAAA,CAA4B,EACpCO,OAAO,CAACN,8NAAAA,CAAoC,EAC5CM,OAAO,CAACL,wNAAAA,CAA8B,EACtCK,OAAO,CAACJ,mMAAAA,CAAS;IAEnBM,mCAAmCH,KAAKE;AAC1C,EAAC;AAkBM,MAAMC,qCAAqC,CAChDH,KACAI;IAEA;;;;;;;;;;GAUC,GACD,MAAMC,iBAAiBL,IAAIM,MAAM;IACjC,MAAMC,WAAWF,eAAeG,UAAU,CAAC,OACvCH,eAAeI,KAAK,CAAC,KACrBJ;IAEJ,+EAA+E;IAC/E,6DAA6D;IAC7D,MAAMK,QAAQH,SACXI,KAAK,CAAC,KACNC,MAAM,CAAC,CAACC,OAASA,QAAQ,CAACA,KAAKL,UAAU,CAAE,KAAEV,+MAAAA,GAAqB;IAErE,IAAIM,KAAKU,MAAM,GAAG,GAAG;QACnBJ,MAAMK,IAAI,CAAIjB,+MAAAA,GAAqB,MAAGM;IACxC,OAAO;QACLM,MAAMK,IAAI,CAAE,KAAEjB,+MAAAA;IAChB;IACAE,IAAIM,MAAM,GAAGI,MAAMI,MAAM,GAAI,MAAGJ,MAAMM,IAAI,CAAC,OAAS;AACtD,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/segment-cache/segment-value-encoding.ts"], "sourcesContent": ["import { PAGE_SEGMENT_KEY } from '../segment'\nimport type { Segment as FlightRouterStateSegment } from '../../../server/app-render/types'\n\n// TypeScript trick to simulate opaque types, like in Flow.\ntype Opaque<K, T> = T & { __brand: K }\n\nexport type SegmentRequestKeyPart = Opaque<'SegmentRequestKeyPart', string>\nexport type SegmentRequestKey = Opaque<'SegmentRequestKey', string>\nexport type SegmentCacheKeyPart = Opaque<'SegmentCacheKeyPart', string>\nexport type SegmentCacheKey = Opaque<'SegmentCacheKey', string>\n\nexport const ROOT_SEGMENT_REQUEST_KEY = '' as SegmentRequestKey\nexport const ROOT_SEGMENT_CACHE_KEY = '' as SegmentCacheKey\n\nexport function createSegmentRequestKeyPart(\n  segment: FlightRouterStateSegment\n): SegmentRequestKeyPart {\n  if (typeof segment === 'string') {\n    if (segment.startsWith(PAGE_SEGMENT_KEY)) {\n      // The Flight Router State type sometimes includes the search params in\n      // the page segment. However, the Segment Cache tracks this as a separate\n      // key. So, we strip the search params here, and then add them back when\n      // the cache entry is turned back into a FlightRouterState. This is an\n      // unfortunate consequence of the FlightRouteState being used both as a\n      // transport type and as a cache key; we'll address this once more of the\n      // Segment Cache implementation has settled.\n      // TODO: We should hoist the search params out of the FlightRouterState\n      // type entirely, This is our plan for dynamic route params, too.\n      return PAGE_SEGMENT_KEY as SegmentRequestKeyPart\n    }\n    const safeName =\n      // TODO: FlightRouterState encodes Not Found routes as \"/_not-found\".\n      // But params typically don't include the leading slash. We should use\n      // a different encoding to avoid this special case.\n      segment === '/_not-found'\n        ? '_not-found'\n        : encodeToFilesystemAndURLSafeString(segment)\n    // Since this is not a dynamic segment, it's fully encoded. It does not\n    // need to be \"hydrated\" with a param value.\n    return safeName as SegmentRequestKeyPart\n  }\n\n  const name = segment[0]\n  const paramType = segment[2]\n  const safeName = encodeToFilesystemAndURLSafeString(name)\n\n  const encodedName = '$' + paramType + '$' + safeName\n  return encodedName as SegmentRequestKeyPart\n}\n\nexport function appendSegmentRequestKeyPart(\n  parentRequestKey: SegmentRequestKey,\n  parallelRouteKey: string,\n  childRequestKeyPart: SegmentRequestKeyPart\n): SegmentRequestKey {\n  // Aside from being filesystem safe, segment keys are also designed so that\n  // each segment and parallel route creates its own subdirectory. Roughly in\n  // the same shape as the source app directory. This is mostly just for easier\n  // debugging (you can open up the build folder and navigate the output); if\n  // we wanted to do we could just use a flat structure.\n\n  // Omit the parallel route key for children, since this is the most\n  // common case. Saves some bytes (and it's what the app directory does).\n  const slotKey =\n    parallelRouteKey === 'children'\n      ? childRequestKeyPart\n      : `@${encodeToFilesystemAndURLSafeString(parallelRouteKey)}/${childRequestKeyPart}`\n  return (parentRequestKey + '/' + slotKey) as SegmentRequestKey\n}\n\nexport function createSegmentCacheKeyPart(\n  requestKeyPart: SegmentRequestKeyPart,\n  segment: FlightRouterStateSegment\n): SegmentCacheKeyPart {\n  if (typeof segment === 'string') {\n    return requestKeyPart as any as SegmentCacheKeyPart\n  }\n  const paramValue = segment[1]\n  const safeValue = encodeToFilesystemAndURLSafeString(paramValue)\n  return (requestKeyPart + '$' + safeValue) as SegmentCacheKeyPart\n}\n\nexport function appendSegmentCacheKeyPart(\n  parentSegmentKey: SegmentCacheKey,\n  parallelRouteKey: string,\n  childCacheKeyPart: SegmentCacheKeyPart\n): SegmentCacheKey {\n  const slotKey =\n    parallelRouteKey === 'children'\n      ? childCacheKeyPart\n      : `@${encodeToFilesystemAndURLSafeString(parallelRouteKey)}/${childCacheKeyPart}`\n  return (parentSegmentKey + '/' + slotKey) as SegmentCacheKey\n}\n\n// Define a regex pattern to match the most common characters found in a route\n// param. It excludes anything that might not be cross-platform filesystem\n// compatible, like |. It does not need to be precise because the fallback is to\n// just base64url-encode the whole parameter, which is fine; we just don't do it\n// by default for compactness, and for easier debugging.\nconst simpleParamValueRegex = /^[a-zA-Z0-9\\-_@]+$/\n\nfunction encodeToFilesystemAndURLSafeString(value: string) {\n  if (simpleParamValueRegex.test(value)) {\n    return value\n  }\n  // If there are any unsafe characters, base64url-encode the entire value.\n  // We also add a ! prefix so it doesn't collide with the simple case.\n  const base64url = btoa(value)\n    .replace(/\\+/g, '-') // Replace '+' with '-'\n    .replace(/\\//g, '_') // Replace '/' with '_'\n    .replace(/=+$/, '') // Remove trailing '='\n  return '!' + base64url\n}\n\nexport function convertSegmentPathToStaticExportFilename(\n  segmentPath: string\n): string {\n  return `__next${segmentPath.replace(/\\//g, '.')}.txt`\n}\n"], "names": ["PAGE_SEGMENT_KEY", "ROOT_SEGMENT_REQUEST_KEY", "ROOT_SEGMENT_CACHE_KEY", "createSegmentRequestKeyPart", "segment", "startsWith", "safeName", "encodeToFilesystemAndURLSafeString", "name", "paramType", "encodedName", "appendSegmentRequestKeyPart", "parentRequestKey", "parallelRouteKey", "childRequestKeyPart", "<PERSON><PERSON><PERSON>", "createSegmentCacheKeyPart", "requestKeyPart", "paramValue", "safeValue", "appendSegmentCacheKeyPart", "parentSegmentKey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simpleParamValueRegex", "value", "test", "base64url", "btoa", "replace", "convertSegmentPathToStaticExportFilename", "segmentPath"], "mappings": ";;;;;;;;;;;;;;;;AAAA,SAASA,gBAAgB,QAAQ,aAAY;;AAWtC,MAAMC,2BAA2B,GAAuB;AACxD,MAAMC,yBAAyB,GAAqB;AAEpD,SAASC,4BACdC,OAAiC;IAEjC,IAAI,OAAOA,YAAY,UAAU;QAC/B,IAAIA,QAAQC,UAAU,CAACL,mLAAAA,GAAmB;YACxC,uEAAuE;YACvE,yEAAyE;YACzE,wEAAwE;YACxE,sEAAsE;YACtE,uEAAuE;YACvE,yEAAyE;YACzE,4CAA4C;YAC5C,uEAAuE;YACvE,iEAAiE;YACjE,OAAOA,mLAAAA;QACT;QACA,MAAMM,WACJ,AACA,qEADqE,CACC;QACtE,mDAAmD;QACnDF,YAAY,gBACR,eACAG,mCAAmCH;QACzC,uEAAuE;QACvE,4CAA4C;QAC5C,OAAOE;IACT;IAEA,MAAME,OAAOJ,OAAO,CAAC,EAAE;IACvB,MAAMK,YAAYL,OAAO,CAAC,EAAE;IAC5B,MAAME,WAAWC,mCAAmCC;IAEpD,MAAME,cAAc,MAAMD,YAAY,MAAMH;IAC5C,OAAOI;AACT;AAEO,SAASC,4BACdC,gBAAmC,EACnCC,gBAAwB,EACxBC,mBAA0C;IAE1C,2EAA2E;IAC3E,2EAA2E;IAC3E,6EAA6E;IAC7E,2EAA2E;IAC3E,sDAAsD;IAEtD,mEAAmE;IACnE,wEAAwE;IACxE,MAAMC,UACJF,qBAAqB,aACjBC,sBACC,MAAGP,mCAAmCM,oBAAkB,MAAGC;IAClE,OAAQF,mBAAmB,MAAMG;AACnC;AAEO,SAASC,0BACdC,cAAqC,EACrCb,OAAiC;IAEjC,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAOa;IACT;IACA,MAAMC,aAAad,OAAO,CAAC,EAAE;IAC7B,MAAMe,YAAYZ,mCAAmCW;IACrD,OAAQD,iBAAiB,MAAME;AACjC;AAEO,SAASC,0BACdC,gBAAiC,EACjCR,gBAAwB,EACxBS,iBAAsC;IAEtC,MAAMP,UACJF,qBAAqB,aACjBS,oBACC,MAAGf,mCAAmCM,oBAAkB,MAAGS;IAClE,OAAQD,mBAAmB,MAAMN;AACnC;AAEA,8EAA8E;AAC9E,0EAA0E;AAC1E,gFAAgF;AAChF,gFAAgF;AAChF,wDAAwD;AACxD,MAAMQ,wBAAwB;AAE9B,SAAShB,mCAAmCiB,KAAa;IACvD,IAAID,sBAAsBE,IAAI,CAACD,QAAQ;QACrC,OAAOA;IACT;IACA,yEAAyE;IACzE,qEAAqE;IACrE,MAAME,YAAYC,KAAKH,OACpBI,OAAO,CAAC,OAAO,KAAK,uBAAuB;KAC3CA,OAAO,CAAC,OAAO,KAAK,uBAAuB;KAC3CA,OAAO,CAAC,OAAO,IAAI,sBAAsB;;IAC5C,OAAO,MAAMF;AACf;AAEO,SAASG,yCACdC,WAAmB;IAEnB,OAAQ,WAAQA,YAAYF,OAAO,CAAC,OAAO,OAAK;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/route-params.ts"], "sourcesContent": ["import type { DynamicParamTypesShort } from '../server/app-render/types'\nimport {\n  addSearchParamsIfPageSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../shared/lib/segment'\nimport { ROOT_SEGMENT_REQUEST_KEY } from '../shared/lib/segment-cache/segment-value-encoding'\nimport {\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n  NEXT_RSC_UNION_QUERY,\n} from './components/app-router-headers'\nimport type { NormalizedSearch } from './components/segment-cache'\nimport type { RSCResponse } from './components/router-reducer/fetch-server-response'\n\nexport type RouteParamValue = string | Array<string> | null\n\nexport type RouteParam = {\n  name: string\n  value: RouteParamValue\n  type: DynamicParamTypesShort\n}\n\nexport function getRenderedSearch(response: RSCResponse): NormalizedSearch {\n  // If the server performed a rewrite, the search params used to render the\n  // page will be different from the params in the request URL. In this case,\n  // the response will include a header that gives the rewritten search query.\n  const rewrittenQuery = response.headers.get(NEXT_REWRITTEN_QUERY_HEADER)\n  if (rewrittenQuery !== null) {\n    return (\n      rewrittenQuery === '' ? '' : '?' + rewrittenQuery\n    ) as NormalizedSearch\n  }\n  // If the header is not present, there was no rewrite, so we use the search\n  // query of the response URL.\n  return urlToUrlWithoutFlightMarker(new URL(response.url))\n    .search as NormalizedSearch\n}\n\nexport function getRenderedPathname(response: RSCResponse): string {\n  // If the server performed a rewrite, the pathname used to render the\n  // page will be different from the pathname in the request URL. In this case,\n  // the response will include a header that gives the rewritten pathname.\n  const rewrittenPath = response.headers.get(NEXT_REWRITTEN_PATH_HEADER)\n  return (\n    rewrittenPath ?? urlToUrlWithoutFlightMarker(new URL(response.url)).pathname\n  )\n}\n\nexport function parseDynamicParamFromURLPart(\n  paramType: DynamicParamTypesShort,\n  pathnameParts: Array<string>,\n  partIndex: number\n): RouteParamValue {\n  // This needs to match the behavior in get-dynamic-param.ts.\n  switch (paramType) {\n    // Catchalls\n    case 'c':\n    case 'ci': {\n      // Catchalls receive all the remaining URL parts. If there are no\n      // remaining pathname parts, return an empty array.\n      return partIndex < pathnameParts.length\n        ? pathnameParts.slice(partIndex).map((s) => encodeURIComponent(s))\n        : []\n    }\n    // Optional catchalls\n    case 'oc': {\n      // Optional catchalls receive all the remaining URL parts, unless this is\n      // the end of the pathname, in which case they return null.\n      return partIndex < pathnameParts.length\n        ? pathnameParts.slice(partIndex).map((s) => encodeURIComponent(s))\n        : null\n    }\n    // Dynamic\n    case 'd':\n    case 'di': {\n      if (partIndex >= pathnameParts.length) {\n        // The route tree expected there to be more parts in the URL than there\n        // actually are. This could happen if the x-nextjs-rewritten-path header\n        // is incorrectly set, or potentially due to bug in Next.js. TODO:\n        // Should this be a hard error? During a prefetch, we can just abort.\n        // During a client navigation, we could trigger a hard refresh. But if\n        // it happens during initial render, we don't really have any\n        // recovery options.\n        return ''\n      }\n      return encodeURIComponent(pathnameParts[partIndex])\n    }\n    default:\n      paramType satisfies never\n      return ''\n  }\n}\n\nexport function doesStaticSegmentAppearInURL(segment: string): boolean {\n  // This is not a parameterized segment; however, we need to determine\n  // whether or not this segment appears in the URL. For example, this route\n  // groups do not appear in the URL, so they should be skipped. Any other\n  // special cases must be handled here.\n  // TODO: Consider encoding this directly into the router tree instead of\n  // inferring it on the client based on the segment type. Something like\n  // a `doesAppearInURL` flag in FlightRouterState.\n  if (\n    segment === ROOT_SEGMENT_REQUEST_KEY ||\n    // For some reason, the loader tree sometimes includes extra __PAGE__\n    // \"layouts\" when part of a parallel route. But it's not a leaf node.\n    // Otherwise, we wouldn't need this special case because pages are\n    // always leaf nodes.\n    // TODO: Investigate why the loader produces these fake page segments.\n    segment.startsWith(PAGE_SEGMENT_KEY) ||\n    // Route groups.\n    (segment[0] === '(' && segment.endsWith(')')) ||\n    segment === DEFAULT_SEGMENT_KEY ||\n    segment === '/_not-found'\n  ) {\n    return false\n  } else {\n    // All other segment types appear in the URL\n    return true\n  }\n}\n\nexport function getCacheKeyForDynamicParam(\n  paramValue: RouteParamValue,\n  renderedSearch: NormalizedSearch\n): string {\n  // This needs to match the logic in get-dynamic-param.ts, until we're able to\n  // unify the various implementations so that these are always computed on\n  // the client.\n  if (typeof paramValue === 'string') {\n    // TODO: Refactor or remove this helper function to accept a string rather\n    // than the whole segment type. Also we can probably just append the\n    // search string instead of turning it into JSON.\n    const pageSegmentWithSearchParams = addSearchParamsIfPageSegment(\n      paramValue,\n      Object.fromEntries(new URLSearchParams(renderedSearch))\n    ) as string\n    return pageSegmentWithSearchParams\n  } else if (paramValue === null) {\n    return ''\n  } else {\n    return paramValue.join('/')\n  }\n}\n\nexport function urlToUrlWithoutFlightMarker(url: URL): URL {\n  const urlWithoutFlightParameters = new URL(url)\n  urlWithoutFlightParameters.searchParams.delete(NEXT_RSC_UNION_QUERY)\n  if (process.env.NODE_ENV === 'production') {\n    if (\n      process.env.__NEXT_CONFIG_OUTPUT === 'export' &&\n      urlWithoutFlightParameters.pathname.endsWith('.txt')\n    ) {\n      const { pathname } = urlWithoutFlightParameters\n      const length = pathname.endsWith('/index.txt') ? 10 : 4\n      // Slice off `/index.txt` or `.txt` from the end of the pathname\n      urlWithoutFlightParameters.pathname = pathname.slice(0, -length)\n    }\n  }\n  return urlWithoutFlightParameters\n}\n\nexport function getParamValueFromCacheKey(\n  paramCacheKey: string,\n  paramType: DynamicParamTypesShort\n) {\n  // Turn the cache key string sent by the server (as part of FlightRouterState)\n  // into a value that can be passed to `useParams` and client components.\n  const isCatchAll = paramType === 'c' || paramType === 'oc'\n  if (isCatchAll) {\n    // Catch-all param keys are a concatenation of the path segments.\n    // See equivalent logic in `getSelectedParams`.\n    // TODO: We should just pass the array directly, rather than concatenate\n    // it to a string and then split it back to an array. It needs to be an\n    // array in some places, like when passing a key React, but we can convert\n    // it at runtime in those places.\n    return paramCacheKey.split('/')\n  }\n  return paramCacheKey\n}\n"], "names": ["addSearchParamsIfPageSegment", "DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "ROOT_SEGMENT_REQUEST_KEY", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_RSC_UNION_QUERY", "getRenderedSearch", "response", "rewritten<PERSON><PERSON><PERSON>", "headers", "get", "urlToUrlWithoutFlightMarker", "URL", "url", "search", "getRenderedPathname", "rewrittenPath", "pathname", "parseDynamicParamFromURLPart", "paramType", "pathnameParts", "partIndex", "length", "slice", "map", "s", "encodeURIComponent", "doesStaticSegmentAppearInURL", "segment", "startsWith", "endsWith", "getCacheKeyForDynamicParam", "paramValue", "renderedSearch", "pageSegmentWithSearchParams", "Object", "fromEntries", "URLSearchParams", "join", "urlWithoutFlightParameters", "searchParams", "delete", "process", "env", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "getParamValueFromCacheKey", "param<PERSON><PERSON><PERSON><PERSON>", "isCatchAll", "split"], "mappings": ";;;;;;;;;;;;;;;;AACA,SACEA,4BAA4B,EAC5BC,mBAAmB,EACnBC,gBAAgB,QACX,wBAAuB;AAC9B,SAASC,wBAAwB,QAAQ,qDAAoD;AAC7F,SACEC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,oBAAoB,QACf,kCAAiC;;;;AAYjC,SAASC,kBAAkBC,QAAqB;IACrD,0EAA0E;IAC1E,2EAA2E;IAC3E,4EAA4E;IAC5E,MAAMC,iBAAiBD,SAASE,OAAO,CAACC,GAAG,CAACN,sNAAAA;IAC5C,IAAII,mBAAmB,MAAM;QAC3B,OACEA,mBAAmB,KAAK,KAAK,MAAMA;IAEvC;IACA,2EAA2E;IAC3E,6BAA6B;IAC7B,OAAOG,4BAA4B,IAAIC,IAAIL,SAASM,GAAG,GACpDC,MAAM;AACX;AAEO,SAASC,oBAAoBR,QAAqB;IACvD,qEAAqE;IACrE,6EAA6E;IAC7E,wEAAwE;IACxE,MAAMS,gBAAgBT,SAASE,OAAO,CAACC,GAAG,CAACP,qNAAAA;IAC3C,OACEa,iBAAAA,OAAAA,gBAAiBL,4BAA4B,IAAIC,IAAIL,SAASM,GAAG,GAAGI,QAAQ;AAEhF;AAEO,SAASC,6BACdC,SAAiC,EACjCC,aAA4B,EAC5BC,SAAiB;IAEjB,4DAA4D;IAC5D,OAAQF;QACN,YAAY;QACZ,KAAK;QACL,KAAK;YAAM;gBACT,iEAAiE;gBACjE,mDAAmD;gBACnD,OAAOE,YAAYD,cAAcE,MAAM,GACnCF,cAAcG,KAAK,CAACF,WAAWG,GAAG,CAAC,CAACC,IAAMC,mBAAmBD,MAC7D,EAAE;YACR;QACA,qBAAqB;QACrB,KAAK;YAAM;gBACT,yEAAyE;gBACzE,2DAA2D;gBAC3D,OAAOJ,YAAYD,cAAcE,MAAM,GACnCF,cAAcG,KAAK,CAACF,WAAWG,GAAG,CAAC,CAACC,IAAMC,mBAAmBD,MAC7D;YACN;QACA,UAAU;QACV,KAAK;QACL,KAAK;YAAM;gBACT,IAAIJ,aAAaD,cAAcE,MAAM,EAAE;oBACrC,uEAAuE;oBACvE,wEAAwE;oBACxE,kEAAkE;oBAClE,qEAAqE;oBACrE,sEAAsE;oBACtE,6DAA6D;oBAC7D,oBAAoB;oBACpB,OAAO;gBACT;gBACA,OAAOI,mBAAmBN,aAAa,CAACC,UAAU;YACpD;QACA;YACEF;YACA,OAAO;IACX;AACF;AAEO,SAASQ,6BAA6BC,OAAe;IAC1D,qEAAqE;IACrE,0EAA0E;IAC1E,wEAAwE;IACxE,sCAAsC;IACtC,wEAAwE;IACxE,uEAAuE;IACvE,iDAAiD;IACjD,IACEA,YAAY1B,oOAAAA,IACZ,qEAAqE;IACrE,qEAAqE;IACrE,kEAAkE;IAClE,qBAAqB;IACrB,sEAAsE;IACtE0B,QAAQC,UAAU,CAAC5B,mLAAAA,KACnB,gBAAgB;IACf2B,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQE,QAAQ,CAAC,QACxCF,YAAY5B,sLAAAA,IACZ4B,YAAY,eACZ;QACA,OAAO;IACT,OAAO;QACL,4CAA4C;QAC5C,OAAO;IACT;AACF;AAEO,SAASG,2BACdC,UAA2B,EAC3BC,cAAgC;IAEhC,6EAA6E;IAC7E,yEAAyE;IACzE,cAAc;IACd,IAAI,OAAOD,eAAe,UAAU;QAClC,0EAA0E;QAC1E,oEAAoE;QACpE,iDAAiD;QACjD,MAAME,kCAA8BnC,+LAAAA,EAClCiC,YACAG,OAAOC,WAAW,CAAC,IAAIC,gBAAgBJ;QAEzC,OAAOC;IACT,OAAO,IAAIF,eAAe,MAAM;QAC9B,OAAO;IACT,OAAO;QACL,OAAOA,WAAWM,IAAI,CAAC;IACzB;AACF;AAEO,SAAS3B,4BAA4BE,GAAQ;IAClD,MAAM0B,6BAA6B,IAAI3B,IAAIC;IAC3C0B,2BAA2BC,YAAY,CAACC,MAAM,CAACpC,+MAAAA;IAC/C,IAAIqC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;IAW3C,OAAOL;AACT;AAEO,SAASO,0BACdC,aAAqB,EACrB5B,SAAiC;IAEjC,8EAA8E;IAC9E,wEAAwE;IACxE,MAAM6B,aAAa7B,cAAc,OAAOA,cAAc;IACtD,IAAI6B,YAAY;QACd,iEAAiE;QACjE,+CAA+C;QAC/C,wEAAwE;QACxE,uEAAuE;QACvE,0EAA0E;QAC1E,iCAAiC;QACjC,OAAOD,cAAcE,KAAK,CAAC;IAC7B;IACA,OAAOF;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/router-reducer/fetch-server-response.ts"], "sourcesContent": ["'use client'\n\n// TODO: Explicitly import from client.browser\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { createFromReadableStream as createFromReadableStreamBrowser } from 'react-server-dom-webpack/client'\n\nimport type {\n  FlightRouterState,\n  NavigationFlightResponse,\n} from '../../../server/app-render/types'\n\nimport type { NEXT_ROUTER_SEGMENT_PREFETCH_HEADER } from '../app-router-headers'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_URL,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n} from '../app-router-headers'\nimport { callServer } from '../../app-call-server'\nimport { findSourceMapURL } from '../../app-find-source-map-url'\nimport { PrefetchKind } from './router-reducer-types'\nimport {\n  normalizeFlightData,\n  prepareFlightRouterStateForRequest,\n  type NormalizedFlightData,\n} from '../../flight-data-helpers'\nimport { getAppBuildId } from '../../app-build-id'\nimport { setCacheBustingSearchParam } from './set-cache-busting-search-param'\nimport { urlToUrlWithoutFlightMarker } from '../../route-params'\n\nconst createFromReadableStream =\n  createFromReadableStreamBrowser as (typeof import('react-server-dom-webpack/client.browser'))['createFromReadableStream']\n\nexport interface FetchServerResponseOptions {\n  readonly flightRouterState: FlightRouterState\n  readonly nextUrl: string | null\n  readonly prefetchKind?: PrefetchKind\n  readonly isHmrRefresh?: boolean\n}\n\nexport type FetchServerResponseResult = {\n  flightData: NormalizedFlightData[] | string\n  canonicalUrl: URL | undefined\n  couldBeIntercepted: boolean\n  prerendered: boolean\n  postponed: boolean\n  staleTime: number\n}\n\nexport type RequestHeaders = {\n  [RSC_HEADER]?: '1'\n  [NEXT_ROUTER_STATE_TREE_HEADER]?: string\n  [NEXT_URL]?: string\n  [NEXT_ROUTER_PREFETCH_HEADER]?: '1' | '2'\n  [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]?: string\n  'x-deployment-id'?: string\n  [NEXT_HMR_REFRESH_HEADER]?: '1'\n  // A header that is only added in test mode to assert on fetch priority\n  'Next-Test-Fetch-Priority'?: RequestInit['priority']\n}\n\nfunction doMpaNavigation(url: string): FetchServerResponseResult {\n  return {\n    flightData: urlToUrlWithoutFlightMarker(\n      new URL(url, location.origin)\n    ).toString(),\n    canonicalUrl: undefined,\n    couldBeIntercepted: false,\n    prerendered: false,\n    postponed: false,\n    staleTime: -1,\n  }\n}\n\nlet abortController = new AbortController()\n\nif (typeof window !== 'undefined') {\n  // Abort any in-flight requests when the page is unloaded, e.g. due to\n  // reloading the page or performing hard navigations. This allows us to ignore\n  // what would otherwise be a thrown TypeError when the browser cancels the\n  // requests.\n  window.addEventListener('pagehide', () => {\n    abortController.abort()\n  })\n\n  // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n  // and the JavaScript execution context is restored by the browser.\n  window.addEventListener('pageshow', () => {\n    abortController = new AbortController()\n  })\n}\n\n/**\n * Fetch the flight data for the provided url. Takes in the current router state\n * to decide what to render server-side.\n */\nexport async function fetchServerResponse(\n  url: URL,\n  options: FetchServerResponseOptions\n): Promise<FetchServerResponseResult> {\n  const { flightRouterState, nextUrl, prefetchKind } = options\n\n  const headers: RequestHeaders = {\n    // Enable flight response\n    [RSC_HEADER]: '1',\n    // Provide the current router state\n    [NEXT_ROUTER_STATE_TREE_HEADER]: prepareFlightRouterStateForRequest(\n      flightRouterState,\n      options.isHmrRefresh\n    ),\n  }\n\n  /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */\n  if (prefetchKind === PrefetchKind.AUTO) {\n    headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n  }\n\n  if (process.env.NODE_ENV === 'development' && options.isHmrRefresh) {\n    headers[NEXT_HMR_REFRESH_HEADER] = '1'\n  }\n\n  if (nextUrl) {\n    headers[NEXT_URL] = nextUrl\n  }\n\n  try {\n    // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n    // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n    // Otherwise, all other prefetches are sent with a \"low\" priority.\n    // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n    const fetchPriority = prefetchKind\n      ? prefetchKind === PrefetchKind.TEMPORARY\n        ? 'high'\n        : 'low'\n      : 'auto'\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        // In \"output: export\" mode, we can't rely on headers to distinguish\n        // between HTML and RSC requests. Instead, we append an extra prefix\n        // to the request.\n        url = new URL(url)\n        if (url.pathname.endsWith('/')) {\n          url.pathname += 'index.txt'\n        } else {\n          url.pathname += '.txt'\n        }\n      }\n    }\n\n    const res = await createFetch(\n      url,\n      headers,\n      fetchPriority,\n      abortController.signal\n    )\n\n    const responseUrl = urlToUrlWithoutFlightMarker(new URL(res.url))\n    const canonicalUrl = res.redirected ? responseUrl : undefined\n\n    const contentType = res.headers.get('content-type') || ''\n    const interception = !!res.headers.get('vary')?.includes(NEXT_URL)\n    const postponed = !!res.headers.get(NEXT_DID_POSTPONE_HEADER)\n    const staleTimeHeaderSeconds = res.headers.get(\n      NEXT_ROUTER_STALE_TIME_HEADER\n    )\n    const staleTime =\n      staleTimeHeaderSeconds !== null\n        ? parseInt(staleTimeHeaderSeconds, 10) * 1000\n        : -1\n    let isFlightResponse = contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        if (!isFlightResponse) {\n          isFlightResponse = contentType.startsWith('text/plain')\n        }\n      }\n    }\n\n    // If fetch returns something different than flight response handle it like a mpa navigation\n    // If the fetch was not 200, we also handle it like a mpa navigation\n    if (!isFlightResponse || !res.ok || !res.body) {\n      // in case the original URL came with a hash, preserve it before redirecting to the new URL\n      if (url.hash) {\n        responseUrl.hash = url.hash\n      }\n\n      return doMpaNavigation(responseUrl.toString())\n    }\n\n    // We may navigate to a page that requires a different Webpack runtime.\n    // In prod, every page will have the same Webpack runtime.\n    // In dev, the Webpack runtime is minimal for each page.\n    // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n    if (process.env.NODE_ENV !== 'production' && !process.env.TURBOPACK) {\n      await (\n        require('../../dev/hot-reloader/app/hot-reloader-app') as typeof import('../../dev/hot-reloader/app/hot-reloader-app')\n      ).waitForWebpackRuntimeHotUpdate()\n    }\n\n    // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n    const flightStream = postponed\n      ? createUnclosingPrefetchStream(res.body)\n      : res.body\n    const response = await (createFromNextReadableStream(\n      flightStream\n    ) as Promise<NavigationFlightResponse>)\n\n    if (getAppBuildId() !== response.b) {\n      return doMpaNavigation(res.url)\n    }\n\n    return {\n      flightData: normalizeFlightData(response.f),\n      canonicalUrl: canonicalUrl,\n      couldBeIntercepted: interception,\n      prerendered: response.S,\n      postponed,\n      staleTime,\n    }\n  } catch (err) {\n    if (!abortController.signal.aborted) {\n      console.error(\n        `Failed to fetch RSC payload for ${url}. Falling back to browser navigation.`,\n        err\n      )\n    }\n\n    // If fetch fails handle it like a mpa navigation\n    // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n    // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n    return {\n      flightData: url.toString(),\n      canonicalUrl: undefined,\n      couldBeIntercepted: false,\n      prerendered: false,\n      postponed: false,\n      staleTime: -1,\n    }\n  }\n}\n\n// This is a subset of the standard Response type. We use a custom type for\n// this so we can limit which details about the response leak into the rest of\n// the codebase. For example, there's some custom logic for manually following\n// redirects, so \"redirected\" in this type could be a composite of multiple\n// browser fetch calls; however, this fact should not leak to the caller.\nexport type RSCResponse = {\n  ok: boolean\n  redirected: boolean\n  headers: Headers\n  body: ReadableStream<Uint8Array> | null\n  status: number\n  url: string\n}\n\nexport async function createFetch(\n  url: URL,\n  headers: RequestHeaders,\n  fetchPriority: 'auto' | 'high' | 'low' | null,\n  signal?: AbortSignal\n): Promise<RSCResponse> {\n  // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n  // cache busting search param) from the request so they're\n  // maximally cacheable.\n\n  if (process.env.__NEXT_TEST_MODE && fetchPriority !== null) {\n    headers['Next-Test-Fetch-Priority'] = fetchPriority\n  }\n\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    headers['x-deployment-id'] = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  const fetchOptions: RequestInit = {\n    // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n    credentials: 'same-origin',\n    headers,\n    priority: fetchPriority || undefined,\n    signal,\n  }\n  // `fetchUrl` is slightly different from `url` because we add a cache-busting\n  // search param to it. This should not leak outside of this function, so we\n  // track them separately.\n  let fetchUrl = new URL(url)\n  setCacheBustingSearchParam(fetchUrl, headers)\n  let browserResponse = await fetch(fetchUrl, fetchOptions)\n\n  // If the server responds with a redirect (e.g. 307), and the redirected\n  // location does not contain the cache busting search param set in the\n  // original request, the response is likely invalid — when following the\n  // redirect, the browser forwards the request headers, but since the cache\n  // busting search param is missing, the server will reject the request due to\n  // a mismatch.\n  //\n  // Ideally, we would be able to intercept the redirect response and perform it\n  // manually, instead of letting the browser automatically follow it, but this\n  // is not allowed by the fetch API.\n  //\n  // So instead, we must \"replay\" the redirect by fetching the new location\n  // again, but this time we'll append the cache busting search param to prevent\n  // a mismatch.\n  //\n  // TODO: We can optimize Next.js's built-in middleware APIs by returning a\n  // custom status code, to prevent the browser from automatically following it.\n  //\n  // This does not affect Server Action-based redirects; those are encoded\n  // differently, as part of the Flight body. It only affects redirects that\n  // occur in a middleware or a third-party proxy.\n\n  let redirected = browserResponse.redirected\n  if (process.env.__NEXT_CLIENT_VALIDATE_RSC_REQUEST_HEADERS) {\n    // This is to prevent a redirect loop. Same limit used by Chrome.\n    const MAX_REDIRECTS = 20\n    for (let n = 0; n < MAX_REDIRECTS; n++) {\n      if (!browserResponse.redirected) {\n        // The server did not perform a redirect.\n        break\n      }\n      const responseUrl = new URL(browserResponse.url, fetchUrl)\n      if (responseUrl.origin !== fetchUrl.origin) {\n        // The server redirected to an external URL. The rest of the logic below\n        // is not relevant, because it only applies to internal redirects.\n        break\n      }\n      if (\n        responseUrl.searchParams.get(NEXT_RSC_UNION_QUERY) ===\n        fetchUrl.searchParams.get(NEXT_RSC_UNION_QUERY)\n      ) {\n        // The redirected URL already includes the cache busting search param.\n        // This was probably intentional. Regardless, there's no reason to\n        // issue another request to this URL because it already has the param\n        // value that we would have added below.\n        break\n      }\n      // The RSC request was redirected. Assume the response is invalid.\n      //\n      // Append the cache busting search param to the redirected URL and\n      // fetch again.\n      fetchUrl = new URL(responseUrl)\n      setCacheBustingSearchParam(fetchUrl, headers)\n      browserResponse = await fetch(fetchUrl, fetchOptions)\n      // We just performed a manual redirect, so this is now true.\n      redirected = true\n    }\n  }\n\n  // Remove the cache busting search param from the response URL, to prevent it\n  // from leaking outside of this function.\n  const responseUrl = new URL(browserResponse.url, fetchUrl)\n  responseUrl.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  const rscResponse: RSCResponse = {\n    url: responseUrl.href,\n\n    // This is true if any redirects occurred, either automatically by the\n    // browser, or manually by us. So it's different from\n    // `browserResponse.redirected`, which only tells us whether the browser\n    // followed a redirect, and only for the last response in the chain.\n    redirected,\n\n    // These can be copied from the last browser response we received. We\n    // intentionally only expose the subset of fields that are actually used\n    // elsewhere in the codebase.\n    ok: browserResponse.ok,\n    headers: browserResponse.headers,\n    body: browserResponse.body,\n    status: browserResponse.status,\n  }\n\n  return rscResponse\n}\n\nexport function createFromNextReadableStream(\n  flightStream: ReadableStream<Uint8Array>\n): Promise<unknown> {\n  return createFromReadableStream(flightStream, {\n    callServer,\n    findSourceMapURL,\n  })\n}\n\nfunction createUnclosingPrefetchStream(\n  originalFlightStream: ReadableStream<Uint8Array>\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream.\n        return\n      }\n    },\n  })\n}\n"], "names": ["createFromReadableStream", "createFromReadableStreamBrowser", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "callServer", "findSourceMapURL", "PrefetchKind", "normalizeFlightData", "prepareFlightRouterStateForRequest", "getAppBuildId", "setCacheBustingSearchParam", "urlToUrlWithoutFlightMarker", "doMpaNavigation", "url", "flightData", "URL", "location", "origin", "toString", "canonicalUrl", "undefined", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "abortController", "AbortController", "window", "addEventListener", "abort", "fetchServerResponse", "options", "flightRouterState", "nextUrl", "prefetchKind", "headers", "isHmrRefresh", "AUTO", "process", "env", "NODE_ENV", "res", "fetchPriority", "TEMPORARY", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "createFetch", "signal", "responseUrl", "redirected", "contentType", "get", "interception", "includes", "staleTimeHeaderSeconds", "parseInt", "isFlightResponse", "startsWith", "ok", "body", "hash", "TURBOPACK", "require", "waitForWebpackRuntimeHotUpdate", "flightStream", "createUnclosingPrefetchStream", "response", "createFromNextReadableStream", "b", "f", "S", "err", "aborted", "console", "error", "__NEXT_TEST_MODE", "NEXT_DEPLOYMENT_ID", "fetchOptions", "credentials", "priority", "fetchUrl", "browserResponse", "fetch", "__NEXT_CLIENT_VALIDATE_RSC_REQUEST_HEADERS", "MAX_REDIRECTS", "n", "searchParams", "delete", "rscResponse", "href", "status", "originalFlightStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "value", "read", "enqueue"], "mappings": ";;;;;;;;AAEA,8CAA8C;AAC9C,6DAA6D;AAC7D,SAASA,4BAA4BC,+BAA+B,QAAQ,kCAAiC;AAQ7G,SACEC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,oBAAoB,EACpBC,QAAQ,EACRC,UAAU,EACVC,uBAAuB,EACvBC,uBAAuB,EACvBC,wBAAwB,EACxBC,6BAA6B,QACxB,wBAAuB;AAC9B,SAASC,UAAU,QAAQ,wBAAuB;AAClD,SAASC,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,YAAY,QAAQ,yBAAwB;AACrD,SACEC,mBAAmB,EACnBC,kCAAkC,QAE7B,4BAA2B;AAClC,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,0BAA0B,QAAQ,mCAAkC;AAC7E,SAASC,2BAA2B,QAAQ,qBAAoB;AAjChE;;;;;;;;;;AAmCA,MAAMlB,2BACJC,yQAAAA;AA8BF,SAASkB,gBAAgBC,GAAW;IAClC,OAAO;QACLC,gBAAYH,+LAAAA,EACV,IAAII,IAAIF,KAAKG,SAASC,MAAM,GAC5BC,QAAQ;QACVC,cAAcC;QACdC,oBAAoB;QACpBC,aAAa;QACbC,WAAW;QACXC,WAAW,CAAC;IACd;AACF;AAEA,IAAIC,kBAAkB,IAAIC;AAE1B,IAAI,OAAOC,WAAW,aAAa;;AAoB5B,eAAeG,oBACpBjB,GAAQ,EACRkB,OAAmC;IAEnC,MAAM,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGH;IAErD,MAAMI,UAA0B;QAC9B,yBAAyB;QACzB,CAACpC,qMAAAA,CAAW,EAAE;QACd,mCAAmC;QACnC,CAACH,wNAAAA,CAA8B,MAAEY,gNAAAA,EAC/BwB,mBACAD,QAAQK,YAAY;IAExB;IAEA;;;;;GAKC,GACD,IAAIF,iBAAiB5B,8NAAAA,CAAa+B,IAAI,EAAE;QACtCF,OAAO,CAACxC,sNAAAA,CAA4B,GAAG;IACzC;IAEA,IAAI2C,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAiBT,QAAQK,YAAY,EAAE;QAClED,OAAO,CAAClC,kNAAAA,CAAwB,GAAG;IACrC;IAEA,IAAIgC,SAAS;QACXE,OAAO,CAACrC,mMAAAA,CAAS,GAAGmC;IACtB;IAEA,IAAI;YAoCqBQ;QAnCvB,wHAAwH;QACxH,4HAA4H;QAC5H,kEAAkE;QAClE,yHAAyH;QACzH,MAAMC,gBAAgBR,eAClBA,iBAAiB5B,8NAAAA,CAAaqC,SAAS,GACrC,SACA,QACF;QAEJ,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;QAc3C,MAAMC,MAAM,MAAMM,YAChBlC,KACAsB,SACAO,eACAjB,gBAAgBuB,MAAM;QAGxB,MAAMC,kBAActC,+LAAAA,EAA4B,IAAII,IAAI0B,IAAI5B,GAAG;QAC/D,MAAMM,eAAesB,IAAIS,UAAU,GAAGD,cAAc7B;QAEpD,MAAM+B,cAAcV,IAAIN,OAAO,CAACiB,GAAG,CAAC,mBAAmB;QACvD,MAAMC,eAAe,CAAC,CAAA,CAAA,CAACZ,mBAAAA,IAAIN,OAAO,CAACiB,GAAG,CAAC,OAAA,KAAA,OAAA,KAAA,IAAhBX,iBAAyBa,QAAQ,CAACxD,mMAAAA,CAAAA;QACzD,MAAMyB,YAAY,CAAC,CAACkB,IAAIN,OAAO,CAACiB,GAAG,CAAClD,mNAAAA;QACpC,MAAMqD,yBAAyBd,IAAIN,OAAO,CAACiB,GAAG,CAC5CjD,wNAAAA;QAEF,MAAMqB,YACJ+B,2BAA2B,OACvBC,SAASD,wBAAwB,MAAM,OACvC,CAAC;QACP,IAAIE,mBAAmBN,YAAYO,UAAU,CAAC1D,kNAAAA;QAE9C,IAAIsC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;QAQ3C,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,CAACiB,oBAAoB,CAAChB,IAAIkB,EAAE,IAAI,CAAClB,IAAImB,IAAI,EAAE;YAC7C,2FAA2F;YAC3F,IAAI/C,IAAIgD,IAAI,EAAE;gBACZZ,YAAYY,IAAI,GAAGhD,IAAIgD,IAAI;YAC7B;YAEA,OAAOjD,gBAAgBqC,YAAY/B,QAAQ;QAC7C;QAEA,uEAAuE;QACvE,0DAA0D;QAC1D,wDAAwD;QACxD,oGAAoG;QACpG,IAAIoB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,CAACF,QAAQC,GAAG,CAACuB,SAAS,EAAE;;QAMrE,2EAA2E;QAC3E,MAAMG,eAAe1C,YACjB2C,8BAA8BzB,IAAImB,IAAI,IACtCnB,IAAImB,IAAI;QACZ,MAAMO,WAAW,MAAOC,6BACtBH;QAGF,QAAIxD,oLAAAA,QAAoB0D,SAASE,CAAC,EAAE;YAClC,OAAOzD,gBAAgB6B,IAAI5B,GAAG;QAChC;QAEA,OAAO;YACLC,gBAAYP,iMAAAA,EAAoB4D,SAASG,CAAC;YAC1CnD,cAAcA;YACdE,oBAAoBgC;YACpB/B,aAAa6C,SAASI,CAAC;YACvBhD;YACAC;QACF;IACF,EAAE,OAAOgD,KAAK;QACZ,IAAI,CAAC/C,gBAAgBuB,MAAM,CAACyB,OAAO,EAAE;YACnCC,QAAQC,KAAK,CACV,qCAAkC9D,MAAI,yCACvC2D;QAEJ;QAEA,iDAAiD;QACjD,qHAAqH;QACrH,iGAAiG;QACjG,OAAO;YACL1D,YAAYD,IAAIK,QAAQ;YACxBC,cAAcC;YACdC,oBAAoB;YACpBC,aAAa;YACbC,WAAW;YACXC,WAAW,CAAC;QACd;IACF;AACF;AAgBO,eAAeuB,YACpBlC,GAAQ,EACRsB,OAAuB,EACvBO,aAA6C,EAC7CM,MAAoB;IAEpB,6EAA6E;IAC7E,0DAA0D;IAC1D,uBAAuB;IAEvB,IAAIV,QAAQC,GAAG,CAACqC,gBAAgB,IAAIlC,kBAAkB,MAAM;;IAI5D,IAAIJ,QAAQC,GAAG,CAACsC,kBAAkB,EAAE;;IAIpC,MAAMC,eAA4B;QAChC,wFAAwF;QACxFC,aAAa;QACb5C;QACA6C,UAAUtC,iBAAiBtB;QAC3B4B;IACF;IACA,6EAA6E;IAC7E,2EAA2E;IAC3E,yBAAyB;IACzB,IAAIiC,WAAW,IAAIlE,IAAIF;QACvBH,4PAAAA,EAA2BuE,UAAU9C;IACrC,IAAI+C,kBAAkB,MAAMC,MAAMF,UAAUH;IAE5C,wEAAwE;IACxE,sEAAsE;IACtE,wEAAwE;IACxE,0EAA0E;IAC1E,6EAA6E;IAC7E,cAAc;IACd,EAAE;IACF,8EAA8E;IAC9E,6EAA6E;IAC7E,mCAAmC;IACnC,EAAE;IACF,yEAAyE;IACzE,8EAA8E;IAC9E,cAAc;IACd,EAAE;IACF,0EAA0E;IAC1E,8EAA8E;IAC9E,EAAE;IACF,wEAAwE;IACxE,0EAA0E;IAC1E,gDAAgD;IAEhD,IAAI5B,aAAagC,gBAAgBhC,UAAU;IAC3C,IAAIZ,QAAQC,GAAG,CAAC6C,0CAA0C,EAAE;;IAoC5D,6EAA6E;IAC7E,yCAAyC;IACzC,MAAMnC,cAAc,IAAIlC,IAAImE,gBAAgBrE,GAAG,EAAEoE;IACjDhC,YAAYsC,YAAY,CAACC,MAAM,CAAC3F,+MAAAA;IAEhC,MAAM4F,cAA2B;QAC/B5E,KAAKoC,YAAYyC,IAAI;QAErB,sEAAsE;QACtE,qDAAqD;QACrD,wEAAwE;QACxE,oEAAoE;QACpExC;QAEA,qEAAqE;QACrE,wEAAwE;QACxE,6BAA6B;QAC7BS,IAAIuB,gBAAgBvB,EAAE;QACtBxB,SAAS+C,gBAAgB/C,OAAO;QAChCyB,MAAMsB,gBAAgBtB,IAAI;QAC1B+B,QAAQT,gBAAgBS,MAAM;IAChC;IAEA,OAAOF;AACT;AAEO,SAASrB,6BACdH,YAAwC;IAExC,OAAOxE,yBAAyBwE,cAAc;oBAC5C7D,oLAAAA;0BACAC,wMAAAA;IACF;AACF;AAEA,SAAS6D,8BACP0B,oBAAgD;IAEhD,0EAA0E;IAC1E,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,8DAA8D;IAC9D,2CAA2C;IAC3C,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,+BAA+B;IAC/B,MAAMC,SAASD,qBAAqBE,SAAS;IAC7C,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMN,OAAOO,IAAI;gBACzC,IAAI,CAACF,MAAM;oBACT,mEAAmE;oBACnE,mBAAmB;oBACnBD,WAAWI,OAAO,CAACF;oBACnB;gBACF;gBACA,qEAAqE;gBACrE,qBAAqB;gBACrB;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/unresolved-thenable.ts"], "sourcesContent": ["/**\n * Create a \"Thenable\" that does not resolve. This is used to suspend indefinitely when data is not available yet.\n */\nexport const unresolvedThenable = {\n  then: () => {},\n} as PromiseLike<void>\n"], "names": ["unresolvedThenable", "then"], "mappings": "AAAA;;CAEC,GACD;;;;AAAO,MAAMA,qBAAqB;IAChCC,MAAM,KAAO;AACf,EAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/navigation-untracked.ts"], "sourcesContent": ["import { useContext } from 'react'\nimport { PathnameContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * This checks to see if the current render has any unknown route parameters.\n * It's used to trigger a different render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */\nfunction hasFallbackRouteParams(): boolean {\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { workUnitAsyncStorage } =\n      require('../../server/app-render/work-unit-async-storage.external') as typeof import('../../server/app-render/work-unit-async-storage.external')\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (!workUnitStore) return false\n\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        return fallbackParams ? fallbackParams.size > 0 : false\n      case 'prerender-legacy':\n      case 'request':\n      case 'prerender-runtime':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n\n    return false\n  }\n\n  return false\n}\n\n/**\n * This returns a `null` value if there are any unknown route parameters, and\n * otherwise returns the pathname from the context. This is an alternative to\n * `usePathname` that is used in the error boundary to avoid rendering the\n * error boundary when there are unknown route parameters. This doesn't throw\n * when accessed with unknown route parameters.\n *\n * @returns\n *\n * @internal\n */\nexport function useUntrackedPathname(): string | null {\n  // If there are any unknown route parameters we would typically throw\n  // an error, but this internal method allows us to return a null value instead\n  // for components that do not propagate the pathname to the static shell (like\n  // the error boundary).\n  if (hasFallbackRouteParams()) {\n    return null\n  }\n\n  // This shouldn't cause any issues related to conditional rendering because\n  // the environment will be consistent for the render.\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(PathnameContext)\n}\n"], "names": ["useContext", "PathnameContext", "hasFallbackRouteParams", "window", "workUnitAsyncStorage", "require", "workUnitStore", "getStore", "type", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "useUntrackedPathname"], "mappings": ";;;;AAAA,SAASA,UAAU,QAAQ,QAAO;AAClC,SAASC,eAAe,QAAQ,uDAAsD;;;AAEtF;;;;;CAKC,GACD,SAASC;IACP,IAAI,OAAOC,WAAW,kBAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,oBAAoB,EAAE,GAC5BC,QAAQ;QAEV,MAAMC,gBAAgBF,qBAAqBG,QAAQ;QACnD,IAAI,CAACD,eAAe,OAAO;QAE3B,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAMC,iBAAiBH,cAAcI,mBAAmB;gBACxD,OAAOD,iBAAiBA,eAAeE,IAAI,GAAG,IAAI;YACpD,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEL;QACJ;QAEA,OAAO;IACT;;;AAGF;AAaO,SAASM;IACd,qEAAqE;IACrE,8EAA8E;IAC9E,8EAA8E;IAC9E,uBAAuB;IACvB,IAAIV,0BAA0B;QAC5B,OAAO;IACT;IAEA,2EAA2E;IAC3E,qDAAqD;IACrD,sDAAsD;IACtD,WAAOF,mNAAAA,EAAWC,kPAAAA;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "HTTP_ERROR_FALLBACK_ERROR_CODE", "isHTTPAccessFallbackError", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "status"], "mappings": ";;;;;;;;;;;;AAAO,MAAMA,wBAAwB;IACnCC,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB,EAAC;AAED,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACP;AAErC,MAAMQ,iCAAiC,2BAA0B;AAajE,SAASC,0BACdC,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWJ,kCACXJ,cAAcW,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASI,4BACdP,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASK,mCACdC,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1106, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;;AAAO,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA;MAIX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN"], "mappings": ";;;;;;;;AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;;AAEpD,MAAMC,sBAAsB,gBAAe;AAE3C,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;MAGX;AAaM,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcd,+MAAAA;AAElB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isHTTPAccessFallbackError", "isRedirectError", "isNextRouterError", "error"], "mappings": ";;;;AAAA,SACEA,yBAAyB,QAEpB,8CAA6C;AACpD,SAASC,eAAe,QAA4B,mBAAkB;;;AAO/D,SAASC,kBACdC,KAAc;IAEd,WAAOF,mMAAAA,EAAgBE,cAAUH,oPAAAA,EAA0BG;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1166, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/router-reducer/create-href-from-url.ts"], "sourcesContent": ["export function createHrefFromUrl(\n  url: Pick<URL, 'pathname' | 'search' | 'hash'>,\n  includeHash: boolean = true\n): string {\n  return url.pathname + url.search + (includeHash ? url.hash : '')\n}\n"], "names": ["createHrefFromUrl", "url", "includeHash", "pathname", "search", "hash"], "mappings": ";;;;AAAO,SAASA,kBACdC,GAA8C,EAC9CC,WAA2B;IAA3BA,IAAAA,gBAAAA,KAAAA,GAAAA,cAAuB;IAEvB,OAAOD,IAAIE,QAAQ,GAAGF,IAAIG,MAAM,GAAIF,CAAAA,cAAcD,IAAII,IAAI,GAAG,EAAC;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/nav-failure-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\n\nexport function handleHardNavError(error: unknown): boolean {\n  if (\n    error &&\n    typeof window !== 'undefined' &&\n    window.next.__pendingUrl &&\n    createHrefFromUrl(new URL(window.location.href)) !==\n      createHrefFromUrl(window.next.__pendingUrl)\n  ) {\n    console.error(\n      `Error occurred during navigation, falling back to hard navigation`,\n      error\n    )\n    window.location.href = window.next.__pendingUrl.toString()\n    return true\n  }\n  return false\n}\n\nexport function useNavFailureHandler() {\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    // this if is only for DCE of the feature flag not conditional\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      const uncaughtExceptionHandler = (\n        evt: ErrorEvent | PromiseRejectionEvent\n      ) => {\n        const error = 'reason' in evt ? evt.reason : evt.error\n        // if we have an unhandled exception/rejection during\n        // a navigation we fall back to a hard navigation to\n        // attempt recovering to a good state\n        handleHardNavError(error)\n      }\n      window.addEventListener('unhandledrejection', uncaughtExceptionHandler)\n      window.addEventListener('error', uncaughtExceptionHandler)\n      return () => {\n        window.removeEventListener('error', uncaughtExceptionHandler)\n        window.removeEventListener(\n          'unhandledrejection',\n          uncaughtExceptionHandler\n        )\n      }\n    }, [])\n  }\n}\n"], "names": ["useEffect", "createHrefFromUrl", "handleHardNavError", "error", "window", "next", "__pendingUrl", "URL", "location", "href", "console", "toString", "useNavFailureHandler", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "uncaughtExceptionHandler", "evt", "reason", "addEventListener", "removeEventListener"], "mappings": ";;;;;;AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,iBAAiB,QAAQ,wCAAuC;;;AAElE,SAASC,mBAAmBC,KAAc;IAC/C,IACEA,SACA,OAAOC,SAAW,eAClBA,OAAOC,IAAI,CAACC,YAAY,QACxBL,sOAAAA,EAAkB,IAAIM,IAAIH,OAAOI,QAAQ,CAACC,IAAI,WAC5CR,sOAAAA,EAAkBG,OAAOC,IAAI,CAACC,YAAY,GAC5C;;IAQF,OAAO;AACT;AAEO,SAASM;IACd,IAAIC,QAAQC,GAAG,CAACC,4BAA4B,EAAE;;AAwBhD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1201, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/handle-isr-error.tsx"], "sourcesContent": ["const workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nexport function HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isRevalidate || store?.isStaticGeneration) {\n      console.error(error)\n      throw error\n    }\n  }\n\n  return null\n}\n"], "names": ["workAsyncStorage", "window", "require", "undefined", "HandleISRError", "error", "store", "getStore", "isRevalidate", "isStaticGeneration", "console"], "mappings": ";;;;AAAA,MAAMA,mBACJ,OAAOC,WAAW,qBAEZC,QAAQ,uKACRF,gBAAgB,GAClBG;AAKC,SAASC,eAAe,KAAyB;IAAzB,IAAA,EAAEC,KAAK,EAAkB,GAAzB;IAC7B,IAAIL,kBAAkB;QACpB,MAAMM,QAAQN,iBAAiBO,QAAQ;QACvC,IAAID,CAAAA,SAAAA,OAAAA,KAAAA,IAAAA,MAAOE,YAAY,KAAA,CAAIF,SAAAA,OAAAA,KAAAA,IAAAA,MAAOG,kBAAkB,GAAE;YACpDC,QAAQL,KAAK,CAACA;YACd,MAAMA;QACR;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/router/utils/html-bots.ts"], "sourcesContent": ["// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\n// Note: The pattern [\\w-]+-Google captures all Google crawlers with \"-Google\" suffix (e.g., Mediapartners-Google, AdsBot-Google, Storebot-Google)\n// as well as crawlers starting with \"Google-\" (e.g., Google-PageRenderer, Google-InspectionTool)\nexport const HTML_LIMITED_BOT_UA_RE =\n  /[\\w-]+-Google|Google-[\\w-]+|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti|googleweblight/i\n"], "names": ["HTML_LIMITED_BOT_UA_RE"], "mappings": "AAAA,6GAA6G;AAC7G,sKAAsK;AACtK,kJAAkJ;AAClJ,iGAAiG;;;;;AAC1F,MAAMA,yBACX,sTAAqT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/router/utils/is-bot.ts"], "sourcesContent": ["import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS.\n// Only the main Googlebot search crawler executes JavaScript, not other Google crawlers.\n// x-ref: https://developers.google.com/search/docs/crawling-indexing/google-common-crawlers\n// This regex specifically matches \"Googlebot\" but NOT \"Mediapartners-Google\", \"AdsBot-Google\", etc.\nconst HEADLESS_BROWSER_BOT_UA_RE = /Googlebot(?!-)|Googlebot$/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n"], "names": ["HTML_LIMITED_BOT_UA_RE", "HEADLESS_BROWSER_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "source", "isDomBotUA", "userAgent", "test", "isHtmlLimitedBotUA", "isBot", "getBotType", "undefined"], "mappings": ";;;;;;;;AAAA,SAASA,sBAAsB,QAAQ,cAAa;;AAEpD,mEAAmE;AACnE,yFAAyF;AACzF,4FAA4F;AAC5F,oGAAoG;AACpG,MAAMC,6BAA6B;AAE5B,MAAMC,gCAAgCF,iNAAAA,CAAuBG,MAAM,CAAA;;AAI1E,SAASC,WAAWC,SAAiB;IACnC,OAAOJ,2BAA2BK,IAAI,CAACD;AACzC;AAEA,SAASE,mBAAmBF,SAAiB;IAC3C,OAAOL,iNAAAA,CAAuBM,IAAI,CAACD;AACrC;AAEO,SAASG,MAAMH,SAAiB;IACrC,OAAOD,WAAWC,cAAcE,mBAAmBF;AACrD;AAEO,SAASI,WAAWJ,SAAiB;IAC1C,IAAID,WAAWC,YAAY;QACzB,OAAO;IACT;IACA,IAAIE,mBAAmBF,YAAY;QACjC,OAAO;IACT;IACA,OAAOK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\nimport { HandleISRError } from './handle-isr-error'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\n\nconst isBotUserAgent =\n  typeof window !== 'undefined' && isBot(window.navigator.userAgent)\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  // global-error, there's no `reset` function;\n  // regular error boundary, there's a `reset` function.\n  reset?: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    //When it's bot request, segment level error boundary will keep rendering the children,\n    // the final error will be caught by the root error boundary and determine wether need to apply graceful degrade.\n    if (this.state.error && !isBotUserAgent) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useUntrackedPathname", "isNextRouterError", "handleHardNavError", "HandleISRError", "isBot", "isBotUserAgent", "window", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "getDerivedStateFromError", "error", "getDerivedStateFromProps", "props", "state", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "previousPathname", "pathname", "render", "errorStyles", "errorScripts", "this", "errorComponent", "reset", "children", "constructor", "setState", "Error<PERSON>ou<PERSON><PERSON>"], "mappings": ";;;;;;;AAEA,OAAOA,WAAyB,QAAO;AACvC,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,KAAK,QAAQ,uCAAsC;AAP5D;;;;;;;;AASA,MAAMC,iBACJ,OAAOC,SAAW,mBAAeF,6MAAAA,EAAME,OAAOC,SAAS,CAACC,SAAS;AA0B5D,MAAMC,6BAA6BV,gNAAAA,CAAMW,SAAS;IASvD,OAAOC,yBAAyBC,KAAY,EAAE;QAC5C,QAAIX,iNAAAA,EAAkBW,QAAQ;YAC5B,+DAA+D;YAC/D,4GAA4G;YAC5G,MAAMA;QACR;QAEA,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAOC,yBACLC,KAAgC,EAChCC,KAAgC,EACE;QAClC,MAAM,EAAEH,KAAK,EAAE,GAAGG;QAElB,iCAAiC;QACjC,8CAA8C;QAC9C,iDAAiD;QACjD,6CAA6C;QAC7C,IAAIC,QAAQC,GAAG,CAACC,4BAA4B,EAAE;;QAU9C;;;;;KAKC,GACD,IAAIJ,MAAMM,QAAQ,KAAKL,MAAMI,gBAAgB,IAAIJ,MAAMH,KAAK,EAAE;YAC5D,OAAO;gBACLA,OAAO;gBACPO,kBAAkBL,MAAMM,QAAQ;YAClC;QACF;QACA,OAAO;YACLR,OAAOG,MAAMH,KAAK;YAClBO,kBAAkBL,MAAMM,QAAQ;QAClC;IACF;IAMA,yIAAyI;IACzIC,SAA0B;QACxB,uFAAuF;QACvF,iHAAiH;QACjH,IAAI,IAAI,CAACN,KAAK,CAACH,KAAK,IAAI,CAACP,gBAAgB;YACvC,OAAA,WAAA,OACE,+NAAA,EAAA,mOAAA,EAAA;;sCACE,8NAAA,EAACF,uMAAAA,EAAAA;wBAAeS,OAAO,IAAI,CAACG,KAAK,CAACH,KAAK;;oBACtC,IAAI,CAACE,KAAK,CAACQ,WAAW;oBACtB,IAAI,CAACR,KAAK,CAACS,YAAY;sCACxB,8NAAA,EAACC,IAAI,CAACV,KAAK,CAACW,cAAc,EAAA;wBACxBb,OAAO,IAAI,CAACG,KAAK,CAACH,KAAK;wBACvBc,OAAO,IAAI,CAACA,KAAK;;;;QAIzB;QAEA,OAAO,IAAI,CAACZ,KAAK,CAACa,QAAQ;IAC5B;IA5EAC,YAAYd,KAAgC,CAAE;QAC5C,KAAK,CAACA,QAAAA,IAAAA,CAoDRY,KAAAA,GAAQ;YACN,IAAI,CAACG,QAAQ,CAAC;gBAAEjB,OAAO;YAAK;QAC9B;QArDE,IAAI,CAACG,KAAK,GAAG;YAAEH,OAAO;YAAMO,kBAAkB,IAAI,CAACL,KAAK,CAACM,QAAQ;QAAC;IACpE;AA0EF;AAWO,SAASU,cAAc,KAO7B;IAP6B,IAAA,EAC5BL,cAAc,EACdH,WAAW,EACXC,YAAY,EACZI,QAAQ,EAGT,GAP6B;IAQ5B,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,oEAAoE;IACpE,MAAMP,eAAWpB,8MAAAA;IACjB,IAAIyB,gBAAgB;QAClB,OAAA,WAAA,OACE,8NAAA,EAAChB,sBAAAA;YACCW,UAAUA;YACVK,gBAAgBA;YAChBH,aAAaA;YACbC,cAAcA;sBAEbI;;IAGP;IAEA,OAAA,WAAA,OAAO,8NAAA,EAAA,mOAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/match-segments.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport const matchSegment = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  // segment is either Array or string\n  if (typeof existingSegment === 'string') {\n    if (typeof segment === 'string') {\n      // Common case: segment is just a string\n      return existingSegment === segment\n    }\n    return false\n  }\n\n  if (typeof segment === 'string') {\n    return false\n  }\n  return existingSegment[0] === segment[0] && existingSegment[1] === segment[1]\n}\n"], "names": ["matchSegment", "existingSegment", "segment"], "mappings": ";;;;AAEO,MAAMA,eAAe,CAC1BC,iBACAC;IAEA,oCAAoC;IACpC,IAAI,OAAOD,oBAAoB,UAAU;QACvC,IAAI,OAAOC,YAAY,UAAU;YAC/B,wCAAwC;YACxC,OAAOD,oBAAoBC;QAC7B;QACA,OAAO;IACT;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAO;IACT;IACA,OAAOD,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE,IAAID,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE;AAC/E,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1409, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/utils/warn-once.ts"], "sourcesContent": ["let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n"], "names": ["warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "add"], "mappings": ";;;;AAAA,IAAIA,WAAW,CAACC,KAAe;AAC/B,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;IACzC,MAAMC,WAAW,IAAIC;IACrBN,WAAW,CAACO;QACV,IAAI,CAACF,SAASG,GAAG,CAACD,MAAM;YACtBE,QAAQC,IAAI,CAACH;QACf;QACAF,SAASM,GAAG,CAACJ;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/router/utils/disable-smooth-scroll.ts"], "sourcesContent": ["import { warnOnce } from '../../utils/warn-once'\n\n/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function disableSmoothScrollDuringRouteTransition(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n\n  const htmlElement = document.documentElement\n  const hasDataAttribute = htmlElement.dataset.scrollBehavior === 'smooth'\n\n  // Since this is a breaking change, this is temporarily flagged\n  // and will be false by default.\n  // In the next major (v16), this will be automatically enabled\n  if (process.env.__NEXT_OPTIMIZE_ROUTER_SCROLL) {\n    if (!hasDataAttribute) {\n      // No smooth scrolling configured, run directly without style manipulation\n      fn()\n      return\n    }\n  } else {\n    // Old behavior: always manipulate styles, but warn about upcoming change\n\n    // Warn if smooth scrolling is detected but no data attribute is present\n    if (\n      process.env.NODE_ENV === 'development' &&\n      !hasDataAttribute &&\n      getComputedStyle(htmlElement).scrollBehavior === 'smooth'\n    ) {\n      warnOnce(\n        'Detected `scroll-behavior: smooth` on the `<html>` element. In a future version, ' +\n          'Next.js will no longer automatically disable smooth scrolling during route transitions. ' +\n          'To prepare for this change, add `data-scroll-behavior=\"smooth\"` to your <html> element. ' +\n          'Learn more: https://nextjs.org/docs/messages/missing-data-scroll-behavior'\n      )\n    }\n  }\n\n  // Proceed with temporarily disabling smooth scrolling\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n"], "names": ["warnOnce", "disableSmoothScrollDuringRouteTransition", "fn", "options", "onlyHashChange", "htmlElement", "document", "documentElement", "hasDataAttribute", "dataset", "scroll<PERSON>eh<PERSON>or", "process", "env", "__NEXT_OPTIMIZE_ROUTER_SCROLL", "NODE_ENV", "getComputedStyle", "existing", "style", "dontForceLayout", "getClientRects"], "mappings": ";;;;AAAA,SAASA,QAAQ,QAAQ,wBAAuB;;AAMzC,SAASC,yCACdC,EAAc,EACdC,OAAqE;IAArEA,IAAAA,YAAAA,KAAAA,GAAAA,UAAmE,CAAC;IAEpE,yEAAyE;IACzE,6FAA6F;IAC7F,IAAIA,QAAQC,cAAc,EAAE;QAC1BF;QACA;IACF;IAEA,MAAMG,cAAcC,SAASC,eAAe;IAC5C,MAAMC,mBAAmBH,YAAYI,OAAO,CAACC,cAAc,KAAK;IAEhE,+DAA+D;IAC/D,gCAAgC;IAChC,8DAA8D;IAC9D,IAAIC,QAAQC,GAAG,CAACC,6BAA6B,EAAE;;SAMxC;QACL,yEAAyE;QAEzE,wEAAwE;QACxE,IACEF,QAAQC,GAAG,CAACE,QAAQ,gCAAK,iBACzB,CAACN,oBACDO,iBAAiBV,aAAaK,cAAc,KAAK,UACjD;gBACAV,yLAAAA,EACE,sFACE,6FACA,6FACA;QAEN;IACF;IAEA,sDAAsD;IACtD,MAAMgB,WAAWX,YAAYY,KAAK,CAACP,cAAc;IACjDL,YAAYY,KAAK,CAACP,cAAc,GAAG;IACnC,IAAI,CAACP,QAAQe,eAAe,EAAE;QAC5B,8EAA8E;QAC9E,4DAA4D;QAC5D,yFAAyF;QACzFb,YAAYc,cAAc;IAC5B;IACAjB;IACAG,YAAYY,KAAK,CAACP,cAAc,GAAGM;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/router-reducer/reducers/get-segment-value.ts"], "sourcesContent": ["import type { Segment } from '../../../../server/app-render/types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n"], "names": ["getSegmentValue", "segment", "Array", "isArray"], "mappings": ";;;;AAEO,SAASA,gBAAgBC,OAAgB;IAC9C,OAAOC,MAAMC,OAAO,CAACF,WAAWA,OAAO,CAAC,EAAE,GAAGA;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1484, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["RedirectStatusCode", "RedirectType", "isRedirectError", "REDIRECT_ERROR_CODE", "actionAsyncStorage", "window", "require", "undefined", "getRedirectError", "url", "type", "statusCode", "TemporaryRedirect", "error", "Error", "digest", "redirect", "getStore", "isAction", "push", "replace", "permanentRedirect", "PermanentRedirect", "getURLFromRedirectError", "split", "slice", "join", "getRedirectTypeFromError", "getRedirectStatusCodeFromError", "Number", "at"], "mappings": ";;;;;;;;;;;;;;AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,YAAY,EAEZC,eAAe,EACfC,mBAAmB,QACd,mBAAkB;;;AAEzB,MAAMC,qBACJ,OAAOC,WAAW,qBAEZC,QAAQ,2KACRF,kBAAkB,GACpBG;AAEC,SAASC,iBACdC,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,aAAiCX,+MAAAA,CAAmBY,iBAAiB;IAErE,MAAMC,QAAQ,OAAA,cAA8B,CAA9B,IAAIC,MAAMX,uMAAAA,GAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CU,MAAME,MAAM,GAAMZ,uMAAAA,GAAoB,MAAGO,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOE;AACT;AAcO,SAASG,SACd,2BAA2B,GAC3BP,GAAW,EACXC,IAAmB;QAEVN;IAATM,QAAAA,OAAAA,OAAAA,OAASN,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,+BAAAA,mBAAoBa,QAAQ,EAAA,KAAA,OAAA,KAAA,IAA5Bb,6BAAgCc,QAAQ,IAC7CjB,gMAAAA,CAAakB,IAAI,GACjBlB,gMAAAA,CAAamB,OAAO;IAExB,MAAMZ,iBAAiBC,KAAKC,MAAMV,+MAAAA,CAAmBY,iBAAiB;AACxE;AAaO,SAASS,kBACd,2BAA2B,GAC3BZ,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,OAAqBT,gMAAAA,CAAamB,OAAO;IAEzC,MAAMZ,iBAAiBC,KAAKC,MAAMV,+MAAAA,CAAmBsB,iBAAiB;AACxE;AAUO,SAASC,wBAAwBV,KAAc;IACpD,IAAI,KAACX,mMAAAA,EAAgBW,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACS,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAASC,yBAAyBd,KAAoB;IAC3D,IAAI,KAACX,mMAAAA,EAAgBW,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAME,MAAM,CAACS,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASI,+BAA+Bf,KAAoB;IACjE,IAAI,KAACX,mMAAAA,EAAgBW,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOe,OAAOhB,MAAME,MAAM,CAACS,KAAK,CAAC,KAAKM,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "notFound", "error", "Error", "digest"], "mappings": ";;;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,KAAED,yPAAAA,GAA+B;AAE1C,SAASE;IACd,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAIC,MAAMH,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1586, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "forbidden", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,KAAED,yPAAAA,GAA+B;AAE1C,SAASE;IACd,IAAI,CAACC,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1626, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "unauthorized", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,KAAED,yPAAAA,GAA+B;AAE1C,SAASE;IACd,IAAI,CAACC,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1667, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(\n    public readonly route: string,\n    public readonly expression: string\n  ) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route \"${route}\".`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for cacheComponents where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  route: string,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(route, expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(route, expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n\nexport function makeDevtoolsIOAwarePromise<T>(underlying: T): Promise<T> {\n  // in React DevTools if we resolve in a setTimeout we will observe\n  // the promise resolution as something that can suspend a boundary or root.\n  return new Promise<T>((resolve) => {\n    // Must use setTimeout to be considered IO React DevTools. setImmediate will not work.\n    setTimeout(() => {\n      resolve(underlying)\n    }, 0)\n  })\n}\n"], "names": ["isHangingPromiseRejectionError", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "route", "expression", "abortListenersBySignal", "WeakMap", "makeHangingPromise", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject", "makeDevtoolsIOAwarePromise", "underlying", "resolve", "setTimeout"], "mappings": ";;;;;;;;AAAO,SAASA,+BACdC,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YACkBC,KAAa,EACbC,UAAkB,CAClC;QACA,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,8KAA8K,EAAED,MAAM,EAAE,CAAC,GAAA,IAAA,CAJhUA,KAAAA,GAAAA,OAAAA,IAAAA,CACAC,UAAAA,GAAAA,YAAAA,IAAAA,CAJFN,MAAAA,GAASC;IASzB;AACF;AAGA,MAAMM,yBAAyB,IAAIC;AAS5B,SAASC,mBACdC,MAAmB,EACnBL,KAAa,EACbC,UAAkB;IAElB,IAAII,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIX,6BAA6BG,OAAOC;IAChE,OAAO;QACL,MAAMQ,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAIf,6BAA6BG,OAAOC;YAE1C,IAAIY,mBAAmBX,uBAAuBY,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCT,uBAAuBe,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB;AAElB,SAASC,2BAA8BC,UAAa;IACzD,kEAAkE;IAClE,2EAA2E;IAC3E,OAAO,IAAIlB,QAAW,CAACmB;QACrB,sFAAsF;QACtFC,WAAW;YACTD,QAAQD;QACV,GAAG;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1733, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["REACT_POSTPONE_TYPE", "Symbol", "for", "isPostpone", "error", "$$typeof"], "mappings": ";;;;AAAA,MAAMA,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASC,WAAWC,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKL;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1745, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BAILOUT_TO_CSR", "BailoutToCSRError", "Error", "constructor", "reason", "digest", "isBailoutToCSRError", "err"], "mappings": "AAAA,+GAA+G;;;;;;;AAC/G,MAAMA,iBAAiB;AAGhB,MAAMC,0BAA0BC;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASL;IAIzB;AACF;AAGO,SAASM,oBAAoBC,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIF,MAAM,KAAKL;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DYNAMIC_ERROR_CODE", "DynamicServerError", "Error", "constructor", "description", "digest", "isDynamicServerError", "err"], "mappings": ";;;;;;AAAA,MAAMA,qBAAqB;AAEpB,MAAMC,2BAA2BC;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCL;IAIpC;AACF;AAEO,SAASM,qBAAqBC,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAIF,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOE,IAAIF,MAAM,KAAKL;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1790, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["NEXT_STATIC_GEN_BAILOUT", "StaticGenBailoutError", "Error", "code", "isStaticGenBailoutError", "error"], "mappings": ";;;;;;AAAA,MAAMA,0BAA0B;AAEzB,MAAMC,8BAA8BC;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOH;;AACzB;AAEO,SAASI,wBACdC,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMF,IAAI,KAAKH;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1812, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/lib/framework/boundary-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\nexport const ROOT_LAYOUT_BOUNDARY_NAME = '__next_root_layout_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;AAAO,MAAMA,yBAAyB,6BAA4B;AAC3D,MAAMC,yBAAyB,6BAA4B;AAC3D,MAAMC,uBAAuB,2BAA0B;AACvD,MAAMC,4BAA4B,gCAA+B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1830, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = (cb: ScheduledFn<void>) => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = (cb: ScheduledFn<void>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["scheduleOnNextTick", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "scheduleImmediate", "setImmediate", "atLeastOneTask", "waitAtLeastOneReactRenderTask", "r"], "mappings": "AAGA;;;;;CAKC,GACD;;;;;;;;;;AAAO,MAAMA,qBAAqB,CAACC;IACjC,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;aAElC;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF,EAAC;AAQM,MAAMS,oBAAoB,CAACT;IAChC,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACLI,aAAaV;IACf;AACF,EAAC;AAOM,SAASW;IACd,OAAO,IAAIV,QAAc,CAACC,UAAYO,kBAAkBP;AAC1D;AAWO,SAASU;IACd,IAAIR,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACL,OAAO,IAAIL,QAAQ,CAACY,IAAMH,aAAaG;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1881, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/invariant-error.ts"], "sourcesContent": ["export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n"], "names": ["InvariantError", "Error", "constructor", "message", "options", "endsWith", "name"], "mappings": ";;;;AAAO,MAAMA,uBAAuBC;IAClCC,YAAYC,OAAe,EAAEC,OAAsB,CAAE;QACnD,KAAK,CACF,gBAAaD,CAAAA,QAAQE,QAAQ,CAAC,OAAOF,UAAUA,UAAU,GAAE,IAAE,8BAC9DC;QAEF,IAAI,CAACE,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1895, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n  PrerenderStoreModernRuntime,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  getRuntimeStagePromise,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from '../../lib/framework/boundary-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicErrorWithStack: null | Error\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspenseAboveBody: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasAllowedDynamic: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspenseAboveBody: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasAllowedDynamic: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'unstable-cache':\n        // Inside cache scopes, marking a scope as dynamic has no effect,\n        // because the outer cache scope creates a cache boundary. This is\n        // subtly different from reading a dynamic data source, which is\n        // forbidden inside a cache scope.\n        return\n      case 'private-cache':\n        // A private cache scope is already dynamic by definition.\n        return\n      case 'prerender-legacy':\n      case 'prerender-ppr':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-ppr':\n        return postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      case 'prerender-legacy':\n        workUnitStore.revalidate = 0\n\n        // We aren't prerendering, but we are generating a static page. We need\n        // to bail out of static generation.\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      case 'request':\n        if (process.env.NODE_ENV !== 'production') {\n          workUnitStore.usedDynamic = true\n        }\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\n/**\n * This function is meant to be used when prerendering without cacheComponents or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(workUnitStore: WorkUnitStore) {\n  switch (workUnitStore.type) {\n    case 'cache':\n    case 'unstable-cache':\n      // Inside cache scopes, marking a scope as dynamic has no effect,\n      // because the outer cache scope creates a cache boundary. This is\n      // subtly different from reading a dynamic data source, which is\n      // forbidden inside a cache scope.\n      return\n    case 'private-cache':\n      // A private cache scope is already dynamic by definition.\n      return\n    case 'prerender':\n    case 'prerender-runtime':\n    case 'prerender-legacy':\n    case 'prerender-ppr':\n    case 'prerender-client':\n      break\n    case 'request':\n      if (process.env.NODE_ENV !== 'production') {\n        workUnitStore.usedDynamic = true\n      }\n      break\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  // It is important that we set this tracking value after aborting. Aborts are executed\n  // synchronously except for the case where you abort during render itself. By setting this\n  // value late we can use it to determine if any of the aborted tasks are the task that\n  // called the sync IO expression in the first place.\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with cacheComponents. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in cacheComponents mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      }\n    }\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n/**\n * Use this function when dynamically prerendering with dynamicIO.\n * We don't want to error, because it's better to return something\n * (and we've already aborted the render at the point where the sync dynamic error occured),\n * but we should log an error server-side.\n * @internal\n */\nexport function warnOnSyncDynamicError(dynamicTracking: DynamicTrackingState) {\n  if (dynamicTracking.syncDynamicErrorWithStack) {\n    // the server did something sync dynamic, likely\n    // leading to an early termination of the prerender.\n    console.error(dynamicTracking.syncDynamicErrorWithStack)\n  }\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createRenderInBrowserAbortSignal(): AbortSignal {\n  const controller = new AbortController()\n  controller.abort(new BailoutToCSRError('Render in Browser'))\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: WorkUnitStore\n): AbortSignal | undefined {\n  switch (workUnitStore.type) {\n    case 'prerender':\n    case 'prerender-runtime':\n      const controller = new AbortController()\n\n      if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If\n        // the input we're waiting on is coming from another cache, we do want\n        // to wait for it so that we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(() => {\n          controller.abort()\n        })\n      } else {\n        // Otherwise we're in the final render and we should already have all\n        // our caches filled.\n        // If the prerender uses stages, we have wait until the runtime stage,\n        // at which point all runtime inputs will be resolved.\n        // (otherwise, a runtime prerender might consider `cookies()` hanging\n        //  even though they'd resolve in the next task.)\n        //\n        // We might still be waiting on some microtasks so we\n        // wait one tick before giving up. When we give up, we still want to\n        // render the content of this cache as deeply as we can so that we can\n        // suspend as deeply as possible in the tree or not at all if we don't\n        // end up waiting for the input.\n        const runtimeStagePromise = getRuntimeStagePromise(workUnitStore)\n        if (runtimeStagePromise) {\n          runtimeStagePromise.then(() =>\n            scheduleOnNextTick(() => controller.abort())\n          )\n        } else {\n          scheduleOnNextTick(() => controller.abort())\n        }\n      }\n\n      return controller.signal\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'request':\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      return undefined\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workStore && workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-client':\n      case 'prerender': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          // We are in a prerender with cacheComponents semantics. We are going to\n          // hang here and never resolve. This will cause the currently\n          // rendering component to effectively be a dynamic hole.\n          React.use(\n            makeHangingPromise(\n              workUnitStore.renderSignal,\n              workStore.route,\n              expression\n            )\n          )\n        }\n        break\n      }\n      case 'prerender-ppr': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          return postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        }\n        break\n      }\n      case 'prerender-runtime':\n        throw new InvariantError(\n          `\\`${expression}\\` was called during a runtime prerender. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'cache':\n      case 'private-cache':\n        throw new InvariantError(\n          `\\`${expression}\\` was called inside a cache scope. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'prerender-legacy':\n      case 'request':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\n\n// Common implicit body tags that React will treat as body when placed directly in html\nconst bodyAndImplicitTags =\n  'body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6'\n\n// Detects when RootLayoutBoundary (our framework marker component) appears\n// after Suspense in the component stack, indicating the root layout is wrapped\n// within a Suspense boundary. Ensures no body/html/implicit-body components are in between.\n//\n// Example matches:\n//   at Suspense (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\n//\n// Or with other components in between (but not body/html/implicit-body):\n//   at Suspense (<anonymous>)\n//   at SomeComponent (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\nconst hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex = new RegExp(\n  `\\\\n\\\\s+at Suspense \\\\(<anonymous>\\\\)(?:(?!\\\\n\\\\s+at (?:${bodyAndImplicitTags}) \\\\(<anonymous>\\\\))[\\\\s\\\\S])*?\\\\n\\\\s+at ${ROOT_LAYOUT_BOUNDARY_NAME} \\\\([^\\\\n]*\\\\)`\n)\n\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  workStore: WorkStore,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (\n    hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex.test(\n      componentStack\n    )\n  ) {\n    // For Suspense within body, the prelude wouldn't be empty so it wouldn't violate the empty static shells rule.\n    // But if you have Suspense above body, the prelude is empty but we allow that because having Suspense\n    // is an explicit signal from the user that they acknowledge the empty shell and want dynamic rendering.\n    dynamicValidation.hasAllowedDynamic = true\n    dynamicValidation.hasSuspenseAboveBody = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    // this error had a Suspense boundary above it so we don't need to report it as a source\n    // of disallowed\n    dynamicValidation.hasAllowedDynamic = true\n    return\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    // This task was the task that called the sync error.\n    dynamicValidation.dynamicErrors.push(\n      clientDynamic.syncDynamicErrorWithStack\n    )\n    return\n  } else {\n    const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentOrOwnerStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */\nfunction createErrorWithComponentOrOwnerStack(\n  message: string,\n  componentStack: string\n) {\n  const ownerStack =\n    process.env.NODE_ENV !== 'production' && React.captureOwnerStack\n      ? React.captureOwnerStack()\n      : null\n\n  const error = new Error(message)\n  error.stack = error.name + ': ' + message + (ownerStack ?? componentStack)\n  return error\n}\n\nexport enum PreludeState {\n  Full = 0,\n  Empty = 1,\n  Errored = 2,\n}\n\nexport function logDisallowedDynamicError(\n  workStore: WorkStore,\n  error: Error\n): void {\n  console.error(error)\n\n  if (!workStore.dev) {\n    if (workStore.hasReadableErrorStacks) {\n      console.error(\n        `To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`\n      )\n    } else {\n      console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`)\n    }\n  }\n}\n\nexport function throwIfDisallowedDynamic(\n  workStore: WorkStore,\n  prelude: PreludeState,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState\n): void {\n  if (prelude !== PreludeState.Full) {\n    if (dynamicValidation.hasSuspenseAboveBody) {\n      // This route has opted into allowing fully dynamic rendering\n      // by including a Suspense boundary above the body. In this case\n      // a lack of a shell is not considered disallowed so we simply return\n      return\n    }\n\n    if (serverDynamic.syncDynamicErrorWithStack) {\n      // There is no shell and the server did something sync dynamic likely\n      // leading to an early termination of the prerender before the shell\n      // could be completed. We terminate the build/validating render.\n      logDisallowedDynamicError(\n        workStore,\n        serverDynamic.syncDynamicErrorWithStack\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    // We didn't have any sync bailouts but there may be user code which\n    // blocked the root. We would have captured these during the prerender\n    // and can log them here and then terminate the build/validating render\n    const dynamicErrors = dynamicValidation.dynamicErrors\n    if (dynamicErrors.length > 0) {\n      for (let i = 0; i < dynamicErrors.length; i++) {\n        logDisallowedDynamicError(workStore, dynamicErrors[i])\n      }\n\n      throw new StaticGenBailoutError()\n    }\n\n    // If we got this far then the only other thing that could be blocking\n    // the root is dynamic Viewport. If this is dynamic then\n    // you need to opt into that by adding a Suspense boundary above the body\n    // to indicate your are ok with fully dynamic rendering.\n    if (dynamicValidation.hasDynamicViewport) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    if (prelude === PreludeState.Empty) {\n      // If we ever get this far then we messed up the tracking of invalid dynamic.\n      // We still adhere to the constraint that you must produce a shell but invite the\n      // user to report this as a bug in Next.js.\n      console.error(\n        `Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`\n      )\n      throw new StaticGenBailoutError()\n    }\n  } else {\n    if (\n      dynamicValidation.hasAllowedDynamic === false &&\n      dynamicValidation.hasDynamicMetadata\n    ) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`\n      )\n      throw new StaticGenBailoutError()\n    }\n  }\n}\n\nexport function delayUntilRuntimeStage<T>(\n  prerenderStore: PrerenderStoreModernRuntime,\n  result: Promise<T>\n): Promise<T> {\n  if (prerenderStore.runtimeStagePromise) {\n    return prerenderStore.runtimeStagePromise.then(() => result)\n  }\n  return result\n}\n"], "names": ["React", "DynamicServerError", "StaticGenBailoutError", "getRuntimeStagePromise", "workUnitAsyncStorage", "workAsyncStorage", "makeHangingPromise", "METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME", "scheduleOnNextTick", "BailoutToCSRError", "InvariantError", "hasPostpone", "unstable_postpone", "createDynamicTrackingState", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicErrorWithStack", "createDynamicValidationState", "hasSuspenseAboveBody", "hasDynamicMetadata", "hasDynamicViewport", "hasAllowedDynamic", "dynamicErrors", "getFirstDynamicReason", "trackingState", "expression", "markCurrentScopeAsDynamic", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "route", "postponeWithTracking", "dynamicTracking", "revalidate", "err", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "throwToInterruptStaticGeneration", "prerenderStore", "trackDynamicDataInDynamicRender", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "undefined", "abortOnSynchronousPlatformIOAccess", "errorWithStack", "trackSynchronousPlatformIOAccessInDev", "requestStore", "prerenderPhase", "abortAndThrowOnSynchronousRequestDataAccess", "prerenderSignal", "signal", "aborted", "warnOnSyncDynamicError", "console", "trackSynchronousRequestDataAccessInDev", "Postpone", "getStore", "assertPostpone", "createPostponeReason", "isDynamicPostpone", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "isPrerenderInterruptedError", "accessedDynamicData", "length", "consumeDynamicAccess", "serverDynamic", "clientDynamic", "formatDynamicAPIAccesses", "filter", "access", "map", "split", "slice", "line", "join", "createRenderInBrowserAbortSignal", "AbortController", "createHangingInputAbortSignal", "cacheSignal", "inputReady", "then", "runtimeStagePromise", "annotateDynamicAccess", "useDynamicRouteParams", "workStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "use", "renderSignal", "hasSuspenseRegex", "bodyAndImplicitTags", "hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex", "RegExp", "hasMetadataRegex", "hasViewportRegex", "hasOutletRegex", "trackAllowedDynamicAccess", "componentStack", "dynamicValidation", "test", "createErrorWithComponentOrOwnerStack", "ownerStack", "captureOwnerStack", "name", "PreludeState", "logDisallowedDynamicError", "dev", "hasReadableErrorStacks", "throwIfDisallowedDynamic", "prelude", "i", "delayUntilRuntimeStage", "result"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAWD,wFAAwF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACxF,OAAOA,WAAW,QAAO;AAEzB,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SACEC,sBAAsB,EACtBC,oBAAoB,QACf,qCAAoC;AAC3C,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBC,yBAAyB,QACpB,yCAAwC;AAC/C,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SAASC,iBAAiB,QAAQ,+CAA8C;AAChF,SAASC,cAAc,QAAQ,mCAAkC;;;;;;;;;;;AAEjE,MAAMC,cAAc,OAAOd,gNAAAA,CAAMe,iBAAiB,KAAK;AAwChD,SAASC,2BACdC,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,2BAA2B;IAC7B;AACF;AAEO,SAASC;IACd,OAAO;QACLC,sBAAsB;QACtBC,oBAAoB;QACpBC,oBAAoB;QACpBC,mBAAmB;QACnBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASC,sBACdC,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASC,0BACdC,KAAgB,EAChBC,aAAuE,EACvEH,UAAkB;IAElB,IAAIG,eAAe;QACjB,OAAQA,cAAcC,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,iEAAiE;gBACjE,kEAAkE;gBAClE,gEAAgE;gBAChE,kCAAkC;gBAClC;YACF,KAAK;gBACH,0DAA0D;gBAC1D;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACED;QACJ;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAID,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIjC,uNAAAA,CACR,CAAC,MAAM,EAAE4B,MAAMM,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIG,eAAe;QACjB,OAAQA,cAAcC,IAAI;YACxB,KAAK;gBACH,OAAOK,qBACLP,MAAMM,KAAK,EACXR,YACAG,cAAcO,eAAe;YAEjC,KAAK;gBACHP,cAAcQ,UAAU,GAAG;gBAE3B,uEAAuE;gBACvE,oCAAoC;gBACpC,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIvC,+MAAAA,CACd,CAAC,MAAM,EAAE6B,MAAMM,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAE,MAAMW,uBAAuB,GAAGb;gBAChCE,MAAMY,iBAAiB,GAAGF,IAAIG,KAAK;gBAEnC,MAAMH;YACR,KAAK;gBACH,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;oBACzCf,cAAcgB,WAAW,GAAG;gBAC9B;gBACA;YACF;gBACEhB;QACJ;IACF;AACF;AAQO,SAASiB,iCACdpB,UAAkB,EAClBE,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIvC,+MAAAA,CACd,CAAC,MAAM,EAAE6B,MAAMM,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAqB,eAAeV,UAAU,GAAG;IAE5BT,MAAMW,uBAAuB,GAAGb;IAChCE,MAAMY,iBAAiB,GAAGF,IAAIG,KAAK;IAEnC,MAAMH;AACR;AASO,SAASU,gCAAgCnB,aAA4B;IAC1E,OAAQA,cAAcC,IAAI;QACxB,KAAK;QACL,KAAK;YACH,iEAAiE;YACjE,kEAAkE;YAClE,gEAAgE;YAChE,kCAAkC;YAClC;QACF,KAAK;YACH,0DAA0D;YAC1D;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH;QACF,KAAK;YACH,IAAIY,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzCf,cAAcgB,WAAW,GAAG;YAC9B;YACA;QACF;YACEhB;IACJ;AACF;AAEA,SAASoB,oCACPf,KAAa,EACbR,UAAkB,EAClBqB,cAAoC;IAEpC,MAAMG,SAAS,CAAC,MAAM,EAAEhB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAMyB,QAAQC,gCAAgCF;IAE9CH,eAAeM,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMf,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBpB,eAAe,CAACuC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfd,OAAOL,gBAAgBrB,sBAAsB,GACzC,IAAIyC,QAAQf,KAAK,GACjBgB;YACJ/B;QACF;IACF;AACF;AAEO,SAASgC,mCACdxB,KAAa,EACbR,UAAkB,EAClBiC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtDa,oCAAoCf,OAAOR,YAAYqB;IACvD,sFAAsF;IACtF,0FAA0F;IAC1F,sFAAsF;IACtF,oDAAoD;IACpD,IAAIX,iBAAiB;QACnB,IAAIA,gBAAgBnB,yBAAyB,KAAK,MAAM;YACtDmB,gBAAgBnB,yBAAyB,GAAG0C;QAC9C;IACF;AACF;AAEO,SAASC,sCACdC,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASC,4CACd7B,KAAa,EACbR,UAAkB,EAClBiC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMiB,kBAAkBjB,eAAeM,UAAU,CAACY,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1BjB,oCAAoCf,OAAOR,YAAYqB;QACvD,sFAAsF;QACtF,0FAA0F;QAC1F,sFAAsF;QACtF,oDAAoD;QACpD,MAAMX,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBnB,yBAAyB,KAAK,MAAM;gBACtDmB,gBAAgBnB,yBAAyB,GAAG0C;YAC9C;QACF;IACF;IACA,MAAMP,gCACJ,CAAC,MAAM,EAAElB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AASO,SAASyC,uBAAuB/B,eAAqC;IAC1E,IAAIA,gBAAgBnB,yBAAyB,EAAE;QAC7C,gDAAgD;QAChD,oDAAoD;QACpDmD,QAAQjB,KAAK,CAACf,gBAAgBnB,yBAAyB;IACzD;AACF;AAGO,MAAMoD,yCACXT,sCAAqC;AAShC,SAASU,SAAS,EAAEpB,MAAM,EAAEhB,KAAK,EAAiB;IACvD,MAAMa,iBAAiB7C,2SAAAA,CAAqBqE,QAAQ;IACpD,MAAMnC,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACND,qBAAqBD,OAAOgB,QAAQd;AACtC;AAEO,SAASD,qBACdD,KAAa,EACbR,UAAkB,EAClBU,eAA4C;IAE5CoC;IACA,IAAIpC,iBAAiB;QACnBA,gBAAgBpB,eAAe,CAACuC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfd,OAAOL,gBAAgBrB,sBAAsB,GACzC,IAAIyC,QAAQf,KAAK,GACjBgB;YACJ/B;QACF;IACF;IAEA5B,gNAAAA,CAAMe,iBAAiB,CAAC4D,qBAAqBvC,OAAOR;AACtD;AAEA,SAAS+C,qBAAqBvC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAASgD,kBAAkBpC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAYqC,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyBtC,IAAYqC,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwB1B,MAAc;IAC7C,OACEA,OAAO2B,QAAQ,CACb,sEAEF3B,OAAO2B,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBH,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMsB,6BAA6B;AAEnC,SAAS1B,gCAAgCuB,OAAe;IACtD,MAAMxB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMmB,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BxB,MAAc4B,MAAM,GAAGD;IACzB,OAAO3B;AACT;AAMO,SAAS6B,4BACd7B,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAc4B,MAAM,KAAKD,8BAC1B,UAAU3B,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASyB,oBACdjE,eAAqC;IAErC,OAAOA,gBAAgBkE,MAAM,GAAG;AAClC;AAEO,SAASC,qBACdC,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcpE,eAAe,CAACuC,IAAI,IAAI8B,cAAcrE,eAAe;IACnE,OAAOoE,cAAcpE,eAAe;AACtC;AAEO,SAASsE,yBACdtE,eAAqC;IAErC,OAAOA,gBACJuE,MAAM,CACL,CAACC,SACC,OAAOA,OAAO/C,KAAK,KAAK,YAAY+C,OAAO/C,KAAK,CAACyC,MAAM,GAAG,GAE7DO,GAAG,CAAC,CAAC,EAAE/D,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLiD,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKf,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIe,KAAKf,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIe,KAAKf,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCgB,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAEnE,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAAS+B;IACP,IAAI,CAAC5D,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI4C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAASsC;IACd,MAAMzC,aAAa,IAAI0C;IACvB1C,WAAWC,KAAK,CAAC,OAAA,cAA0C,CAA1C,IAAI5C,oNAAAA,CAAkB,sBAAtB,qBAAA;eAAA;oBAAA;sBAAA;IAAyC;IAC1D,OAAO2C,WAAWY,MAAM;AAC1B;AAOO,SAAS+B,8BACdnE,aAA4B;IAE5B,OAAQA,cAAcC,IAAI;QACxB,KAAK;QACL,KAAK;YACH,MAAMuB,aAAa,IAAI0C;YAEvB,IAAIlE,cAAcoE,WAAW,EAAE;gBAC7B,sEAAsE;gBACtE,sEAAsE;gBACtE,8DAA8D;gBAC9DpE,cAAcoE,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;oBAC1C9C,WAAWC,KAAK;gBAClB;YACF,OAAO;gBACL,qEAAqE;gBACrE,qBAAqB;gBACrB,sEAAsE;gBACtE,sDAAsD;gBACtD,qEAAqE;gBACrE,iDAAiD;gBACjD,EAAE;gBACF,qDAAqD;gBACrD,oEAAoE;gBACpE,sEAAsE;gBACtE,sEAAsE;gBACtE,gCAAgC;gBAChC,MAAM8C,0BAAsBnG,6SAAAA,EAAuB4B;gBACnD,IAAIuE,qBAAqB;oBACvBA,oBAAoBD,IAAI,CAAC,QACvB1F,6KAAAA,EAAmB,IAAM4C,WAAWC,KAAK;gBAE7C,OAAO;wBACL7C,6KAAAA,EAAmB,IAAM4C,WAAWC,KAAK;gBAC3C;YACF;YAEA,OAAOD,WAAWY,MAAM;QAC1B,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAOR;QACT;YACE5B;IACJ;AACF;AAEO,SAASwE,sBACd3E,UAAkB,EAClBqB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBpB,eAAe,CAACuC,IAAI,CAAC;YACnCd,OAAOL,gBAAgBrB,sBAAsB,GACzC,IAAIyC,QAAQf,KAAK,GACjBgB;YACJ/B;QACF;IACF;AACF;AAEO,SAAS4E,sBAAsB5E,UAAkB;IACtD,MAAM6E,YAAYpG,uRAAAA,CAAiBoE,QAAQ;IAC3C,MAAM1C,gBAAgB3B,2SAAAA,CAAqBqE,QAAQ;IACnD,IAAIgC,aAAa1E,eAAe;QAC9B,OAAQA,cAAcC,IAAI;YACxB,KAAK;YACL,KAAK;gBAAa;oBAChB,MAAM0E,iBAAiB3E,cAAc4E,mBAAmB;oBACxD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;wBAC7C,wEAAwE;wBACxE,6DAA6D;wBAC7D,wDAAwD;wBACxD5G,gNAAAA,CAAM6G,GAAG,KACPvG,oMAAAA,EACEyB,cAAc+E,YAAY,EAC1BL,UAAUrE,KAAK,EACfR;oBAGN;oBACA;gBACF;YACA,KAAK;gBAAiB;oBACpB,MAAM8E,iBAAiB3E,cAAc4E,mBAAmB;oBACxD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;wBAC7C,OAAOvE,qBACLoE,UAAUrE,KAAK,EACfR,YACAG,cAAcO,eAAe;oBAEjC;oBACA;gBACF;YACA,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIzB,4LAAAA,CACR,CAAC,EAAE,EAAEe,WAAW,uEAAuE,EAAEA,WAAW,+EAA+E,CAAC,GADhL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIf,4LAAAA,CACR,CAAC,EAAE,EAAEe,WAAW,iEAAiE,EAAEA,WAAW,+EAA+E,CAAC,GAD1K,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEG;QACJ;IACF;AACF;AAEA,MAAMgF,mBAAmB;AAEzB,uFAAuF;AACvF,MAAMC,sBACJ;AAEF,2EAA2E;AAC3E,+EAA+E;AAC/E,4FAA4F;AAC5F,EAAE;AACF,mBAAmB;AACnB,8BAA8B;AAC9B,mDAAmD;AACnD,EAAE;AACF,yEAAyE;AACzE,8BAA8B;AAC9B,mCAAmC;AACnC,mDAAmD;AACnD,MAAMC,4DAA4D,IAAIC,OACpE,CAAC,uDAAuD,EAAEF,oBAAoB,yCAAyC,EAAEtG,6MAAAA,CAA0B,cAAc,CAAC;AAGpK,MAAMyG,mBAAmB,IAAID,OAC3B,CAAC,UAAU,EAAE3G,0MAAAA,CAAuB,QAAQ,CAAC;AAE/C,MAAM6G,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAE1G,0MAAAA,CAAuB,QAAQ,CAAC;AAE/C,MAAM6G,iBAAiB,IAAIH,OAAO,CAAC,UAAU,EAAEzG,wMAAAA,CAAqB,QAAQ,CAAC;AAEtE,SAAS6G,0BACdb,SAAoB,EACpBc,cAAsB,EACtBC,iBAAyC,EACzCjC,aAAmC;IAEnC,IAAI8B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIJ,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBlG,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI8F,iBAAiBK,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBjG,kBAAkB,GAAG;QACvC;IACF,OAAO,IACL0F,0DAA0DQ,IAAI,CAC5DF,iBAEF;QACA,+GAA+G;QAC/G,sGAAsG;QACtG,wGAAwG;QACxGC,kBAAkBhG,iBAAiB,GAAG;QACtCgG,kBAAkBnG,oBAAoB,GAAG;QACzC;IACF,OAAO,IAAI0F,iBAAiBU,IAAI,CAACF,iBAAiB;QAChD,wFAAwF;QACxF,gBAAgB;QAChBC,kBAAkBhG,iBAAiB,GAAG;QACtC;IACF,OAAO,IAAI+D,cAAcpE,yBAAyB,EAAE;QAClD,qDAAqD;QACrDqG,kBAAkB/F,aAAa,CAACgC,IAAI,CAClC8B,cAAcpE,yBAAyB;QAEzC;IACF,OAAO;QACL,MAAM0D,UAAU,CAAC,OAAO,EAAE4B,UAAUrE,KAAK,CAAC,2NAA2N,CAAC;QACtQ,MAAMiB,QAAQqE,qCAAqC7C,SAAS0C;QAC5DC,kBAAkB/F,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA;;;CAGC,GACD,SAASqE,qCACP7C,OAAe,EACf0C,cAAsB;IAEtB,MAAMI,aACJ/E,QAAQC,GAAG,CAACC,QAAQ,gCAAK,gBAAgB9C,gNAAAA,CAAM4H,iBAAiB,GAC5D5H,gNAAAA,CAAM4H,iBAAiB,KACvB;IAEN,MAAMvE,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMmB,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BxB,MAAMV,KAAK,GAAGU,MAAMwE,IAAI,GAAG,OAAOhD,UAAW8C,CAAAA,cAAcJ,cAAa;IACxE,OAAOlE;AACT;AAEO,IAAKyE,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;WAAAA;MAIX;AAEM,SAASC,0BACdtB,SAAoB,EACpBpD,KAAY;IAEZiB,QAAQjB,KAAK,CAACA;IAEd,IAAI,CAACoD,UAAUuB,GAAG,EAAE;QAClB,IAAIvB,UAAUwB,sBAAsB,EAAE;YACpC3D,QAAQjB,KAAK,CACX,CAAC,iIAAiI,EAAEoD,UAAUrE,KAAK,CAAC,2CAA2C,CAAC;QAEpM,OAAO;YACLkC,QAAQjB,KAAK,CAAC,CAAC;0EACqD,EAAEoD,UAAUrE,KAAK,CAAC;qGACS,CAAC;QAClG;IACF;AACF;AAEO,SAAS8F,yBACdzB,SAAoB,EACpB0B,OAAqB,EACrBX,iBAAyC,EACzClC,aAAmC;IAEnC,IAAI6C,YAAAA,GAA+B;QACjC,IAAIX,kBAAkBnG,oBAAoB,EAAE;YAC1C,6DAA6D;YAC7D,gEAAgE;YAChE,qEAAqE;YACrE;QACF;QAEA,IAAIiE,cAAcnE,yBAAyB,EAAE;YAC3C,qEAAqE;YACrE,oEAAoE;YACpE,gEAAgE;YAChE4G,0BACEtB,WACAnB,cAAcnE,yBAAyB;YAEzC,MAAM,IAAIjB,uNAAAA;QACZ;QAEA,oEAAoE;QACpE,sEAAsE;QACtE,uEAAuE;QACvE,MAAMuB,gBAAgB+F,kBAAkB/F,aAAa;QACrD,IAAIA,cAAc2D,MAAM,GAAG,GAAG;YAC5B,IAAK,IAAIgD,IAAI,GAAGA,IAAI3G,cAAc2D,MAAM,EAAEgD,IAAK;gBAC7CL,0BAA0BtB,WAAWhF,aAAa,CAAC2G,EAAE;YACvD;YAEA,MAAM,IAAIlI,uNAAAA;QACZ;QAEA,sEAAsE;QACtE,wDAAwD;QACxD,yEAAyE;QACzE,wDAAwD;QACxD,IAAIsH,kBAAkBjG,kBAAkB,EAAE;YACxC+C,QAAQjB,KAAK,CACX,CAAC,OAAO,EAAEoD,UAAUrE,KAAK,CAAC,8QAA8Q,CAAC;YAE3S,MAAM,IAAIlC,uNAAAA;QACZ;QAEA,IAAIiI,YAAAA,GAAgC;YAClC,6EAA6E;YAC7E,iFAAiF;YACjF,2CAA2C;YAC3C7D,QAAQjB,KAAK,CACX,CAAC,OAAO,EAAEoD,UAAUrE,KAAK,CAAC,wGAAwG,CAAC;YAErI,MAAM,IAAIlC,uNAAAA;QACZ;IACF,OAAO;QACL,IACEsH,kBAAkBhG,iBAAiB,KAAK,SACxCgG,kBAAkBlG,kBAAkB,EACpC;YACAgD,QAAQjB,KAAK,CACX,CAAC,OAAO,EAAEoD,UAAUrE,KAAK,CAAC,8PAA8P,CAAC;YAE3R,MAAM,IAAIlC,uNAAAA;QACZ;IACF;AACF;AAEO,SAASmI,uBACdpF,cAA2C,EAC3CqF,MAAkB;IAElB,IAAIrF,eAAeqD,mBAAmB,EAAE;QACtC,OAAOrD,eAAeqD,mBAAmB,CAACD,IAAI,CAAC,IAAMiC;IACvD;IACA,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2513, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/unstable-rethrow.server.ts"], "sourcesContent": ["import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["isHangingPromiseRejectionError", "isPostpone", "isBailoutToCSRError", "isNextRouterError", "isDynamicPostpone", "isDynamicServerError", "unstable_rethrow", "error", "Error", "cause"], "mappings": ";;;;AAAA,SAASA,8BAA8B,QAAQ,uCAAsC;AACrF,SAASC,UAAU,QAAQ,4CAA2C;AACtE,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,iBAAiB,QAAQ,4CAA2C;AAC7E,SAASC,oBAAoB,QAAQ,yBAAwB;;;;;;;AAEtD,SAASC,iBAAiBC,KAAc;IAC7C,QACEJ,iNAAAA,EAAkBI,cAClBL,sNAAAA,EAAoBK,cACpBF,iNAAAA,EAAqBE,cACrBH,2MAAAA,EAAkBG,cAClBN,uMAAAA,EAAWM,cACXP,gNAAAA,EAA+BO,QAC/B;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBC,SAAS,WAAWD,OAAO;QAC9CD,iBAAiBC,MAAME,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2541, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n"], "names": ["unstable_rethrow", "window", "require"], "mappings": "AAAA;;;;;;CAMC,GACD;;;;AAAO,MAAMA,mBACX,OAAOC,WAAW,qBAEZC,QAAQ,4HACRF,gBAAgB,GAEhBE,QAAQ,8BACRF,gBAAgB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2556, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends Error {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport function unstable_isUnrecognizedActionError(): boolean {\n  throw new Error(\n    '`unstable_isUnrecognizedActionError` can only be used on the client.'\n  )\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParamsError", "Error", "constructor", "ReadonlyURLSearchParams", "URLSearchParams", "append", "delete", "set", "sort", "unstable_isUnrecognizedActionError", "redirect", "permanentRedirect", "RedirectType", "notFound", "forbidden", "unauthorized", "unstable_rethrow"], "mappings": "AAAA,cAAc;;;;;;AAkCd,SAASU,QAAQ,EAAEC,iBAAiB,QAAQ,aAAY;AACxD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,QAAQ,QAAQ,cAAa;AACtC,SAASC,SAAS,QAAQ,cAAa;AACvC,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,qBAAoB;AAtCrD,MAAMhB,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMC,gCAAgCC;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,SAAS;QACP,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,MAAM;QACJ,MAAM,IAAIP;IACZ;IACA,wKAAwK,GACxKQ,OAAO;QACL,MAAM,IAAIR;IACZ;AACF;AAEO,SAASS;IACd,MAAM,OAAA,cAEL,CAFK,IAAIR,MACR,yEADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2606, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/unrecognized-action-error.ts"], "sourcesContent": ["export class UnrecognizedActionError extends Error {\n  constructor(...args: ConstructorParameters<typeof Error>) {\n    super(...args)\n    this.name = 'UnrecognizedActionError'\n  }\n}\n\n/**\n * Check whether a server action call failed because the server action was not recognized by the server.\n * This can happen if the client and the server are not from the same deployment.\n *\n * Example usage:\n * ```ts\n * try {\n *   await myServerAction();\n * } catch (err) {\n *   if (unstable_isUnrecognizedActionError(err)) {\n *     // The client is from a different deployment than the server.\n *     // Reloading the page will fix this mismatch.\n *     window.alert(\"Please refresh the page and try again\");\n *     return;\n *   }\n * }\n * ```\n * */\nexport function unstable_isUnrecognizedActionError(\n  error: unknown\n): error is UnrecognizedActionError {\n  return !!(\n    error &&\n    typeof error === 'object' &&\n    error instanceof UnrecognizedActionError\n  )\n}\n"], "names": ["UnrecognizedActionError", "Error", "constructor", "args", "name", "unstable_isUnrecognizedActionError", "error"], "mappings": ";;;;;;AAAO,MAAMA,gCAAgCC;IAC3CC,YAAY,GAAGC,IAAyC,CAAE;QACxD,KAAK,IAAIA;QACT,IAAI,CAACC,IAAI,GAAG;IACd;AACF;AAoBO,SAASC,mCACdC,KAAc;IAEd,OAAO,CAAC,CACNA,CAAAA,SACA,OAAOA,UAAU,YACjBA,iBAAiBN,uBAAsB;AAE3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2625, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/bailout-to-client-rendering.ts"], "sourcesContent": ["import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../../server/app-render/work-unit-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-runtime':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        throw new BailoutToCSRError(reason)\n      case 'request':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n"], "names": ["BailoutToCSRError", "workAsyncStorage", "workUnitAsyncStorage", "bailoutToClientRendering", "reason", "workStore", "getStore", "forceStatic", "workUnitStore", "type"], "mappings": ";;;;AAAA,SAASA,iBAAiB,QAAQ,+CAA8C;AAChF,SAASC,gBAAgB,QAAQ,sDAAqD;AACtF,SAASC,oBAAoB,QAAQ,2DAA0D;;;;AAExF,SAASC,yBAAyBC,MAAc;IACrD,MAAMC,YAAYJ,uRAAAA,CAAiBK,QAAQ;IAE3C,IAAID,aAAAA,OAAAA,KAAAA,IAAAA,UAAWE,WAAW,EAAE;IAE5B,MAAMC,gBAAgBN,2SAAAA,CAAqBI,QAAQ;IAEnD,IAAIE,eAAe;QACjB,OAAQA,cAAcC,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAA6B,CAA7B,IAAIT,oNAAAA,CAAkBI,SAAtB,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4B;YACpC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEI;QACJ;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2665, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/navigation.ts"], "sourcesContent": ["import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\nexport { unstable_isUnrecognizedActionError } from './unrecognized-action-error'\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["useContext", "useMemo", "AppRouterContext", "LayoutRouterContext", "SearchParamsContext", "PathnameContext", "PathParamsContext", "getSegmentValue", "PAGE_SEGMENT_KEY", "DEFAULT_SEGMENT_KEY", "ReadonlyURLSearchParams", "useDynamicRouteParams", "window", "require", "undefined", "useSearchParams", "searchParams", "readonlySearchParams", "bailoutToClientRendering", "usePathname", "ServerInsertedHTMLContext", "useServerInsertedHTML", "useRouter", "router", "Error", "useParams", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "startsWith", "push", "useSelectedLayoutSegments", "context", "parentTree", "useSelectedLayoutSegment", "selectedLayoutSegments", "length", "selectedLayoutSegment", "unstable_isUnrecognizedActionError", "notFound", "forbidden", "unauthorized", "redirect", "permanentRedirect", "RedirectType", "unstable_rethrow"], "mappings": ";;;;;;;;;;;;;;AAGA,SAASA,UAAU,EAAEC,OAAO,QAAQ,QAAO;AAC3C,SACEC,gBAAgB,EAChBC,mBAAmB,QAEd,qDAAoD;AAC3D,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,QACZ,uDAAsD;AAC7D,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,2BAA0B;AAChF,SAASC,uBAAuB,QAAQ,4BAA2B;AAmFnE,wBAAwB;AACxB,SACEU,yBAAyB,EACzBC,qBAAqB,QAChB,uDAAsD;AA2K7D,SAAS0B,kCAAkC,QAAQ,8BAA6B;;;;;;;AAhQhF,MAAMpC,wBACJ,OAAOC,WAAW,qBAEZC,QAAQ,sHACRF,qBAAqB,GACvBG;AAuBC,SAASC;IACd,MAAMC,mBAAehB,mNAAAA,EAAWI,sPAAAA;IAEhC,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMa,uBAAuBhB,oNAAAA,EAAQ;QACnC,IAAI,CAACe,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,IAAIN,uOAAAA,CAAwBM;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOJ,WAAW,kBAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEM,wBAAwB,EAAE,GAChCL,QAAQ;QACV,mEAAmE;QACnEK,yBAAyB;IAC3B;IAEA,OAAOD;AACT;AAoBO,SAASE;IACdR,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,8EAA8E;IAC9E,0EAA0E;IAC1E,WAAOX,mNAAAA,EAAWK,kPAAAA;AACpB;;AA2BO,SAASiB;IACd,MAAMC,aAASvB,mNAAAA,EAAWE,iPAAAA;IAC1B,IAAIqB,WAAW,MAAM;QACnB,MAAM,OAAA,cAAwD,CAAxD,IAAIC,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D;IAEA,OAAOD;AACT;AAoBO,SAASE;IACdd,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,WAAOX,mNAAAA,EAAWM,oPAAAA;AACpB;AAEA,0EAA0E,GAC1E,wBAAwB;AACxB,SAASoB,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,UAAAA,KAAAA,GAAAA,QAAQ;IACRC,IAAAA,gBAAAA,KAAAA,GAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,KAAA,OAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,IAAIM,mBAAe9B,0OAAAA,EAAgB6B;IAEnC,IAAI,CAACC,gBAAgBA,aAAaC,UAAU,CAAC9B,mLAAAA,GAAmB;QAC9D,OAAOsB;IACT;IAEAA,YAAYS,IAAI,CAACF;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AA4BO,SAASU,0BACdZ,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAM8B,cAAUzC,mNAAAA,EAAWG,oPAAAA;IAC3B,wFAAwF;IACxF,IAAI,CAACsC,SAAS,OAAO;IAErB,OAAOf,6BAA6Be,QAAQC,UAAU,EAAEd;AAC1D;AAqBO,SAASe,yBACdf,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAMiC,yBAAyBJ,0BAA0BZ;IAEzD,IAAI,CAACgB,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJlB,qBAAqB,aACjBgB,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,0BAA0BrC,sLAAAA,GAC7B,OACAqC;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2790, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/redirect-boundary.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect } from 'react'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useRouter } from './navigation'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { RedirectType, isRedirectError } from './redirect-error'\n\ninterface RedirectBoundaryProps {\n  router: AppRouterInstance\n  children: React.ReactNode\n}\n\nfunction HandleRedirect({\n  redirect,\n  reset,\n  redirectType,\n}: {\n  redirect: string\n  redirectType: RedirectType\n  reset: () => void\n}) {\n  const router = useRouter()\n\n  useEffect(() => {\n    React.startTransition(() => {\n      if (redirectType === RedirectType.push) {\n        router.push(redirect, {})\n      } else {\n        router.replace(redirect, {})\n      }\n      reset()\n    })\n  }, [redirect, redirectType, reset, router])\n\n  return null\n}\n\nexport class RedirectErrorBoundary extends React.Component<\n  RedirectBoundaryProps,\n  { redirect: string | null; redirectType: RedirectType | null }\n> {\n  constructor(props: RedirectBoundaryProps) {\n    super(props)\n    this.state = { redirect: null, redirectType: null }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isRedirectError(error)) {\n      const url = getURLFromRedirectError(error)\n      const redirectType = getRedirectTypeFromError(error)\n      return { redirect: url, redirectType }\n    }\n    // Re-throw if error is not for redirect\n    throw error\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    const { redirect, redirectType } = this.state\n    if (redirect !== null && redirectType !== null) {\n      return (\n        <HandleRedirect\n          redirect={redirect}\n          redirectType={redirectType}\n          reset={() => this.setState({ redirect: null })}\n        />\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport function RedirectBoundary({ children }: { children: React.ReactNode }) {\n  const router = useRouter()\n  return (\n    <RedirectErrorBoundary router={router}>{children}</RedirectErrorBoundary>\n  )\n}\n"], "names": ["React", "useEffect", "useRouter", "getRedirectTypeFromError", "getURLFromRedirectError", "RedirectType", "isRedirectError", "HandleRedirect", "redirect", "reset", "redirectType", "router", "startTransition", "push", "replace", "RedirectErrorBoundary", "Component", "getDerivedStateFromError", "error", "url", "render", "state", "setState", "props", "children", "constructor", "RedirectBoundary"], "mappings": ";;;;;;;AACA,OAAOA,SAASC,SAAS,QAAQ,QAAO;AAExC,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,aAAY;AAC9E,SAASC,YAAY,EAAEC,eAAe,QAAQ,mBAAkB;AALhE;;;;;;AAYA,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,QAAQ,EACRC,KAAK,EACLC,YAAY,EAKb,GARuB;IAStB,MAAMC,aAAST,sMAAAA;QAEfD,kNAAAA,EAAU;QACRD,gNAAAA,CAAMY,eAAe,CAAC;YACpB,IAAIF,iBAAiBL,gMAAAA,CAAaQ,IAAI,EAAE;gBACtCF,OAAOE,IAAI,CAACL,UAAU,CAAC;YACzB,OAAO;gBACLG,OAAOG,OAAO,CAACN,UAAU,CAAC;YAC5B;YACAC;QACF;IACF,GAAG;QAACD;QAAUE;QAAcD;QAAOE;KAAO;IAE1C,OAAO;AACT;AAEO,MAAMI,8BAA8Bf,gNAAAA,CAAMgB,SAAS;IASxD,OAAOC,yBAAyBC,KAAU,EAAE;QAC1C,QAAIZ,mMAAAA,EAAgBY,QAAQ;YAC1B,MAAMC,UAAMf,kMAAAA,EAAwBc;YACpC,MAAMR,mBAAeP,mMAAAA,EAAyBe;YAC9C,OAAO;gBAAEV,UAAUW;gBAAKT;YAAa;QACvC;QACA,wCAAwC;QACxC,MAAMQ;IACR;IAEA,yIAAyI;IACzIE,SAA0B;QACxB,MAAM,EAAEZ,QAAQ,EAAEE,YAAY,EAAE,GAAG,IAAI,CAACW,KAAK;QAC7C,IAAIb,aAAa,QAAQE,iBAAiB,MAAM;YAC9C,OAAA,WAAA,OACE,8NAAA,EAACH,gBAAAA;gBACCC,UAAUA;gBACVE,cAAcA;gBACdD,OAAO,IAAM,IAAI,CAACa,QAAQ,CAAC;wBAAEd,UAAU;oBAAK;;QAGlD;QAEA,OAAO,IAAI,CAACe,KAAK,CAACC,QAAQ;IAC5B;IA7BAC,YAAYF,KAA4B,CAAE;QACxC,KAAK,CAACA;QACN,IAAI,CAACF,KAAK,GAAG;YAAEb,UAAU;YAAME,cAAc;QAAK;IACpD;AA2BF;AAEO,SAASgB,iBAAiB,KAA2C;IAA3C,IAAA,EAAEF,QAAQ,EAAiC,GAA3C;IAC/B,MAAMb,aAAST,sMAAAA;IACf,OAAA,WAAA,OACE,8NAAA,EAACa,uBAAAA;QAAsBJ,QAAQA;kBAASa;;AAE5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2874, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/http-access-fallback/error-boundary.tsx"], "sourcesContent": ["'use client'\n\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */\n\nimport React, { useContext } from 'react'\nimport { useUntrackedPathname } from '../navigation-untracked'\nimport {\n  HTTPAccessErrorStatus,\n  getAccessFallbackHTTPStatus,\n  getAccessFallbackErrorTypeByStatus,\n  isHTTPAccessFallbackError,\n} from './http-access-fallback'\nimport { warnOnce } from '../../../shared/lib/utils/warn-once'\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime'\n\ninterface HTTPAccessFallbackBoundaryProps {\n  notFound?: React.ReactNode\n  forbidden?: React.ReactNode\n  unauthorized?: React.ReactNode\n  children: React.ReactNode\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessFallbackErrorBoundaryProps\n  extends HTTPAccessFallbackBoundaryProps {\n  pathname: string | null\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessBoundaryState {\n  triggeredStatus: number | undefined\n  previousPathname: string | null\n}\n\nclass HTTPAccessFallbackErrorBoundary extends React.Component<\n  HTTPAccessFallbackErrorBoundaryProps,\n  HTTPAccessBoundaryState\n> {\n  constructor(props: HTTPAccessFallbackErrorBoundaryProps) {\n    super(props)\n    this.state = {\n      triggeredStatus: undefined,\n      previousPathname: props.pathname,\n    }\n  }\n\n  componentDidCatch(): void {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      this.props.missingSlots &&\n      this.props.missingSlots.size > 0 &&\n      // A missing children slot is the typical not-found case, so no need to warn\n      !this.props.missingSlots.has('children')\n    ) {\n      let warningMessage =\n        'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' +\n        'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n'\n\n      const formattedSlots = Array.from(this.props.missingSlots)\n        .sort((a, b) => a.localeCompare(b))\n        .map((slot) => `@${slot}`)\n        .join(', ')\n\n      warningMessage += 'Missing slots: ' + formattedSlots\n\n      warnOnce(warningMessage)\n    }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isHTTPAccessFallbackError(error)) {\n      const httpStatus = getAccessFallbackHTTPStatus(error)\n      return {\n        triggeredStatus: httpStatus,\n      }\n    }\n    // Re-throw if error is not for 404\n    throw error\n  }\n\n  static getDerivedStateFromProps(\n    props: HTTPAccessFallbackErrorBoundaryProps,\n    state: HTTPAccessBoundaryState\n  ): HTTPAccessBoundaryState | null {\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n      return {\n        triggeredStatus: undefined,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      triggeredStatus: state.triggeredStatus,\n      previousPathname: props.pathname,\n    }\n  }\n\n  render() {\n    const { notFound, forbidden, unauthorized, children } = this.props\n    const { triggeredStatus } = this.state\n    const errorComponents = {\n      [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n      [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n      [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized,\n    }\n\n    if (triggeredStatus) {\n      const isNotFound =\n        triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound\n      const isForbidden =\n        triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden\n      const isUnauthorized =\n        triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized\n\n      // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n      if (!(isNotFound || isForbidden || isUnauthorized)) {\n        return children\n      }\n\n      return (\n        <>\n          <meta name=\"robots\" content=\"noindex\" />\n          {process.env.NODE_ENV === 'development' && (\n            <meta\n              name=\"boundary-next-error\"\n              content={getAccessFallbackErrorTypeByStatus(triggeredStatus)}\n            />\n          )}\n          {errorComponents[triggeredStatus]}\n        </>\n      )\n    }\n\n    return children\n  }\n}\n\nexport function HTTPAccessFallbackBoundary({\n  notFound,\n  forbidden,\n  unauthorized,\n  children,\n}: HTTPAccessFallbackBoundaryProps) {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these error can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  const missingSlots = useContext(MissingSlotContext)\n  const hasErrorFallback = !!(notFound || forbidden || unauthorized)\n\n  if (hasErrorFallback) {\n    return (\n      <HTTPAccessFallbackErrorBoundary\n        pathname={pathname}\n        notFound={notFound}\n        forbidden={forbidden}\n        unauthorized={unauthorized}\n        missingSlots={missingSlots}\n      >\n        {children}\n      </HTTPAccessFallbackErrorBoundary>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "useUntrackedPathname", "HTTPAccessErrorStatus", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isHTTPAccessFallbackError", "warnOnce", "MissingSlotContext", "HTTPAccessFallbackErrorBoundary", "Component", "componentDidCatch", "process", "env", "NODE_ENV", "props", "missingSlots", "size", "has", "warningMessage", "formattedSlots", "Array", "from", "sort", "a", "b", "localeCompare", "map", "slot", "join", "getDerivedStateFromError", "error", "httpStatus", "triggeredStatus", "getDerivedStateFromProps", "state", "pathname", "previousPathname", "undefined", "render", "notFound", "forbidden", "unauthorized", "children", "errorComponents", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "isNotFound", "isForbidden", "isUnauthorized", "meta", "name", "content", "constructor", "HTTPAccessFallbackBoundary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;AAEA;;;;;;;;;CASC,GAED,OAAOA,SAASC,UAAU,QAAQ,QAAO;AACzC,SAASC,oBAAoB,QAAQ,0BAAyB;AAC9D,SACEC,qBAAqB,EACrBC,2BAA2B,EAC3BC,kCAAkC,EAClCC,yBAAyB,QACpB,yBAAwB;AAC/B,SAASC,QAAQ,QAAQ,sCAAqC;AAC9D,SAASC,kBAAkB,QAAQ,wDAAuD;AAtB1F;;;;;;;AA2CA,MAAMC,wCAAwCT,gNAAAA,CAAMU,SAAS;IAY3DC,oBAA0B;QACxB,IACEC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzB,IAAI,CAACC,KAAK,CAACC,YAAY,IACvB,IAAI,CAACD,KAAK,CAACC,YAAY,CAACC,IAAI,GAAG,KAC/B,4EAA4E;QAC5E,CAAC,IAAI,CAACF,KAAK,CAACC,YAAY,CAACE,GAAG,CAAC,aAC7B;YACA,IAAIC,iBACF,4HACA;YAEF,MAAMC,iBAAiBC,MAAMC,IAAI,CAAC,IAAI,CAACP,KAAK,CAACC,YAAY,EACtDO,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,GAAG,CAAC,CAACC,OAAU,MAAGA,MAClBC,IAAI,CAAC;YAERV,kBAAkB,oBAAoBC;gBAEtCb,yLAAAA,EAASY;QACX;IACF;IAEA,OAAOW,yBAAyBC,KAAU,EAAE;QAC1C,IAAIzB,wPAAAA,EAA0ByB,QAAQ;YACpC,MAAMC,iBAAa5B,sPAAAA,EAA4B2B;YAC/C,OAAO;gBACLE,iBAAiBD;YACnB;QACF;QACA,mCAAmC;QACnC,MAAMD;IACR;IAEA,OAAOG,yBACLnB,KAA2C,EAC3CoB,KAA8B,EACE;QAChC;;;;;KAKC,GACD,IAAIpB,MAAMqB,QAAQ,KAAKD,MAAME,gBAAgB,IAAIF,MAAMF,eAAe,EAAE;YACtE,OAAO;gBACLA,iBAAiBK;gBACjBD,kBAAkBtB,MAAMqB,QAAQ;YAClC;QACF;QACA,OAAO;YACLH,iBAAiBE,MAAMF,eAAe;YACtCI,kBAAkBtB,MAAMqB,QAAQ;QAClC;IACF;IAEAG,SAAS;QACP,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAAC5B,KAAK;QAClE,MAAM,EAAEkB,eAAe,EAAE,GAAG,IAAI,CAACE,KAAK;QACtC,MAAMS,kBAAkB;YACtB,CAACzC,gPAAAA,CAAsB0C,SAAS,CAAC,EAAEL;YACnC,CAACrC,gPAAAA,CAAsB2C,SAAS,CAAC,EAAEL;YACnC,CAACtC,gPAAAA,CAAsB4C,YAAY,CAAC,EAAEL;QACxC;QAEA,IAAIT,iBAAiB;YACnB,MAAMe,aACJf,oBAAoB9B,gPAAAA,CAAsB0C,SAAS,IAAIL;YACzD,MAAMS,cACJhB,oBAAoB9B,gPAAAA,CAAsB2C,SAAS,IAAIL;YACzD,MAAMS,iBACJjB,oBAAoB9B,gPAAAA,CAAsB4C,YAAY,IAAIL;YAE5D,kGAAkG;YAClG,IAAI,CAAEM,CAAAA,cAAcC,eAAeC,cAAa,GAAI;gBAClD,OAAOP;YACT;YAEA,OAAA,WAAA,OACE,+NAAA,EAAA,mOAAA,EAAA;;sCACE,8NAAA,EAACQ,QAAAA;wBAAKC,MAAK;wBAASC,SAAQ;;oBAC3BzC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAA,WAAA,OACxB,8NAAA,EAACqC,QAAAA;wBACCC,MAAK;wBACLC,aAAShD,6PAAAA,EAAmC4B;;oBAG/CW,eAAe,CAACX,gBAAgB;;;QAGvC;QAEA,OAAOU;IACT;IArGAW,YAAYvC,KAA2C,CAAE;QACvD,KAAK,CAACA;QACN,IAAI,CAACoB,KAAK,GAAG;YACXF,iBAAiBK;YACjBD,kBAAkBtB,MAAMqB,QAAQ;QAClC;IACF;AAgGF;AAEO,SAASmB,2BAA2B,KAKT;IALS,IAAA,EACzCf,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACwB,GALS;IAMzC,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,mEAAmE;IACnE,MAAMP,eAAWlC,8MAAAA;IACjB,MAAMc,mBAAef,mNAAAA,EAAWO,mPAAAA;IAChC,MAAMgD,mBAAmB,CAAC,CAAEhB,CAAAA,YAAYC,aAAaC,YAAW;IAEhE,IAAIc,kBAAkB;QACpB,OAAA,WAAA,OACE,8NAAA,EAAC/C,iCAAAA;YACC2B,UAAUA;YACVI,UAAUA;YACVC,WAAWA;YACXC,cAAcA;YACd1B,cAAcA;sBAEb2B;;IAGP;IAEA,OAAA,WAAA,OAAO,8NAAA,EAAA,mOAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3004, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/router-reducer/create-router-cache-key.ts"], "sourcesContent": ["import type { Segment } from '../../../server/app-render/types'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\nexport function createRouterCacheKey(\n  segment: Segment,\n  withoutSearchParameters: boolean = false\n) {\n  // if the segment is an array, it means it's a dynamic segment\n  // for example, ['lang', 'en', 'd']. We need to convert it to a string to store it as a cache node key.\n  if (Array.isArray(segment)) {\n    return `${segment[0]}|${segment[1]}|${segment[2]}`\n  }\n\n  // Page segments might have search parameters, ie __PAGE__?foo=bar\n  // When `withoutSearchParameters` is true, we only want to return the page segment\n  if (withoutSearchParameters && segment.startsWith(PAGE_SEGMENT_KEY)) {\n    return PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n"], "names": ["PAGE_SEGMENT_KEY", "createRouterCache<PERSON>ey", "segment", "withoutSearchParameters", "Array", "isArray", "startsWith"], "mappings": ";;;;AACA,SAASA,gBAAgB,QAAQ,8BAA6B;;AAEvD,SAASC,qBACdC,OAAgB,EAChBC,uBAAwC;IAAxCA,IAAAA,4BAAAA,KAAAA,GAAAA,0BAAmC;IAEnC,8DAA8D;IAC9D,uGAAuG;IACvG,IAAIC,MAAMC,OAAO,CAACH,UAAU;QAC1B,OAAUA,OAAO,CAAC,EAAE,GAAC,MAAGA,OAAO,CAAC,EAAE,GAAC,MAAGA,OAAO,CAAC,EAAE;IAClD;IAEA,kEAAkE;IAClE,kFAAkF;IAClF,IAAIC,2BAA2BD,QAAQI,UAAU,CAACN,mLAAAA,GAAmB;QACnE,OAAOA,mLAAAA;IACT;IAEA,OAAOE;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3028, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC,GACD;;;;AAAO,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3042, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["ensureLeadingSlash", "isGroupSegment", "normalizeAppPath", "route", "split", "reduce", "pathname", "segment", "index", "segments", "length", "normalizeRscURL", "url", "replace"], "mappings": ";;;;;;AAAA,SAASA,kBAAkB,QAAQ,uCAAsC;AACzE,SAASC,cAAc,QAAQ,gBAAe;;;AAqBvC,SAASC,iBAAiBC,KAAa;IAC5C,WAAOH,wNAAAA,EACLG,MAAMC,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,QAAIL,iLAAAA,EAAeM,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASC,MAAM,GAAG,GAC5B;YACA,OAAOJ;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASI,gBAAgBC,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3080, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/router/utils/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["normalizeAppPath", "INTERCEPTION_ROUTE_MARKERS", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "extractInterceptionRouteInformation", "interceptingRoute", "marker", "interceptedRoute", "Error", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;;;;AAAA,SAASA,gBAAgB,QAAQ,cAAa;;AAGvC,MAAMC,6BAA6B;IACxC;IACA;IACA;IACA;CACD,CAAS;AAEH,SAASC,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLL,2BAA2BI,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASC,oCAAoCP,IAAY;IAC9D,IAAIQ,mBACFC,QACAC;IAEF,KAAK,MAAMP,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCQ,SAASX,2BAA2BI,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAIK,QAAQ;;YACT,CAACD,mBAAmBE,iBAAiB,GAAGV,KAAKC,KAAK,CAACQ,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,iCAA8BX,OAAK,sFADhC,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAQ,wBAAoBX,2MAAAA,EAAiBW,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAoB,MAAGA;YACzB,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIG,MACP,iCAA8BX,OAAK,iEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAU,mBAAmBF,kBAChBP,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACH,kBACPI,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIJ,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMK,yBAAyBP,kBAAkBP,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,OAAA,cAEL,CAFK,IAAIL,MACP,iCAA8BX,OAAK,oEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAU,mBAAmBK,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACH,kBACPI,IAAI,CAAC;YACR;QACF;YACE,MAAM,OAAA,cAAyC,CAAzC,IAAIH,MAAM,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;IAClD;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3171, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/router-reducer/reducers/has-interception-route-in-current-tree.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../../../server/app-render/types'\nimport { isInterceptionRouteAppPath } from '../../../../shared/lib/router/utils/interception-routes'\n\nexport function hasInterceptionRouteInCurrentTree([\n  segment,\n  parallelRoutes,\n]: FlightRouterState): boolean {\n  // If we have a dynamic segment, it's marked as an interception route by the presence of the `i` suffix.\n  if (Array.isArray(segment) && (segment[2] === 'di' || segment[2] === 'ci')) {\n    return true\n  }\n\n  // If segment is not an array, apply the existing string-based check\n  if (typeof segment === 'string' && isInterceptionRouteAppPath(segment)) {\n    return true\n  }\n\n  // Iterate through parallelRoutes if they exist\n  if (parallelRoutes) {\n    for (const key in parallelRoutes) {\n      if (hasInterceptionRouteInCurrentTree(parallelRoutes[key])) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n"], "names": ["isInterceptionRouteAppPath", "hasInterceptionRouteInCurrentTree", "segment", "parallelRoutes", "Array", "isArray", "key"], "mappings": ";;;;AACA,SAASA,0BAA0B,QAAQ,0DAAyD;;AAE7F,SAASC,kCAAkC,KAG9B;IAH8B,IAAA,CAChDC,SACAC,eACkB,GAH8B;IAIhD,wGAAwG;IACxG,IAAIC,MAAMC,OAAO,CAACH,YAAaA,CAAAA,OAAO,CAAC,EAAE,KAAK,QAAQA,OAAO,CAAC,EAAE,KAAK,IAAG,GAAI;QAC1E,OAAO;IACT;IAEA,oEAAoE;IACpE,IAAI,OAAOA,YAAY,gBAAYF,+NAAAA,EAA2BE,UAAU;QACtE,OAAO;IACT;IAEA,+CAA+C;IAC/C,IAAIC,gBAAgB;QAClB,IAAK,MAAMG,OAAOH,eAAgB;YAChC,IAAIF,kCAAkCE,cAAc,CAACG,IAAI,GAAG;gBAC1D,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3201, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/bfcache.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../server/app-render/types'\nimport { useState } from 'react'\n\n// When the flag is disabled, only track the currently active tree\nconst MAX_BF_CACHE_ENTRIES = process.env.__NEXT_ROUTER_BF_CACHE ? 3 : 1\n\nexport type RouterBFCacheEntry = {\n  tree: FlightRouterState\n  stateKey: string\n  // The entries form a linked list, sorted in order of most recently active.\n  next: RouterBFCacheEntry | null\n}\n\n/**\n * Keeps track of the most recent N trees (FlightRouterStates) that were active\n * at a certain segment level. E.g. for a segment \"/a/b/[param]\", this hook\n * tracks the last N param values that the router rendered for N.\n *\n * The result of this hook precisely determines the number and order of\n * trees that are rendered in parallel at their segment level.\n *\n * The purpose of this cache is to we can preserve the React and DOM state of\n * some number of inactive trees, by rendering them in an <Activity> boundary.\n * That means it would not make sense for the the lifetime of the cache to be\n * any longer than the lifetime of the React tree; e.g. if the hook were\n * unmounted, then the React tree would be, too. So, we use React state to\n * manage it.\n *\n * Note that we don't store the RSC data for the cache entries in this hook —\n * the data for inactive segments is stored in the parent CacheNode, which\n * *does* have a longer lifetime than the React tree. This hook only determines\n * which of those trees should have their *state* preserved, by <Activity>.\n */\nexport function useRouterBFCache(\n  activeTree: FlightRouterState,\n  activeStateKey: string\n): RouterBFCacheEntry {\n  // The currently active entry. The entries form a linked list, sorted in\n  // order of most recently active. This allows us to reuse parts of the list\n  // without cloning, unless there's a reordering or removal.\n  // TODO: Once we start tracking back/forward history at each route level,\n  // we should use the history order instead. In other words, when traversing\n  // to an existing entry as a result of a popstate event, we should maintain\n  // the existing order instead of moving it to the front of the list. I think\n  // an initial implementation of this could be to pass an incrementing id\n  // to history.pushState/replaceState, then use that here for ordering.\n  const [prevActiveEntry, setPrevActiveEntry] = useState<RouterBFCacheEntry>(\n    () => {\n      const initialEntry: RouterBFCacheEntry = {\n        tree: activeTree,\n        stateKey: activeStateKey,\n        next: null,\n      }\n      return initialEntry\n    }\n  )\n\n  if (prevActiveEntry.tree === activeTree) {\n    // Fast path. The active tree hasn't changed, so we can reuse the\n    // existing state.\n    return prevActiveEntry\n  }\n\n  // The route tree changed. Note that this doesn't mean that the tree changed\n  // *at this level* — the change may be due to a child route. Either way, we\n  // need to either add or update the router tree in the bfcache.\n  //\n  // The rest of the code looks more complicated than it actually is because we\n  // can't mutate the state in place; we have to copy-on-write.\n\n  // Create a new entry for the active cache key. This is the head of the new\n  // linked list.\n  const newActiveEntry: RouterBFCacheEntry = {\n    tree: activeTree,\n    stateKey: activeStateKey,\n    next: null,\n  }\n\n  // We need to append the old list onto the new list. If the head of the new\n  // list was already present in the cache, then we'll need to clone everything\n  // that came before it. Then we can reuse the rest.\n  let n = 1\n  let oldEntry: RouterBFCacheEntry | null = prevActiveEntry\n  let clonedEntry: RouterBFCacheEntry = newActiveEntry\n  while (oldEntry !== null && n < MAX_BF_CACHE_ENTRIES) {\n    if (oldEntry.stateKey === activeStateKey) {\n      // Fast path. This entry in the old list that corresponds to the key that\n      // is now active. We've already placed a clone of this entry at the front\n      // of the new list. We can reuse the rest of the old list without cloning.\n      // NOTE: We don't need to worry about eviction in this case because we\n      // haven't increased the size of the cache, and we assume the max size\n      // is constant across renders. If we were to change it to a dynamic limit,\n      // then the implementation would need to account for that.\n      clonedEntry.next = oldEntry.next\n      break\n    } else {\n      // Clone the entry and append it to the list.\n      n++\n      const entry: RouterBFCacheEntry = {\n        tree: oldEntry.tree,\n        stateKey: oldEntry.stateKey,\n        next: null,\n      }\n      clonedEntry.next = entry\n      clonedEntry = entry\n    }\n    oldEntry = oldEntry.next\n  }\n\n  setPrevActiveEntry(newActiveEntry)\n  return newActiveEntry\n}\n"], "names": ["useState", "MAX_BF_CACHE_ENTRIES", "process", "env", "__NEXT_ROUTER_BF_CACHE", "useRouterBFCache", "activeTree", "activeStateKey", "prevActiveEntry", "setPrevActiveEntry", "initialEntry", "tree", "stateKey", "next", "newActiveEntry", "n", "oldEntry", "clonedEntry", "entry"], "mappings": ";;;;AACA,SAASA,QAAQ,QAAQ,QAAO;;AAEhC,kEAAkE;AAClE,MAAMC,uBAAuBC,QAAQC,GAAG,CAACC,sBAAsB,IAAG,0BAAI;AA6B/D,SAASC,iBACdC,UAA6B,EAC7BC,cAAsB;IAEtB,wEAAwE;IACxE,2EAA2E;IAC3E,2DAA2D;IAC3D,yEAAyE;IACzE,2EAA2E;IAC3E,2EAA2E;IAC3E,4EAA4E;IAC5E,wEAAwE;IACxE,sEAAsE;IACtE,MAAM,CAACC,iBAAiBC,mBAAmB,OAAGT,iNAAAA,EAC5C;QACE,MAAMU,eAAmC;YACvCC,MAAML;YACNM,UAAUL;YACVM,MAAM;QACR;QACA,OAAOH;IACT;IAGF,IAAIF,gBAAgBG,IAAI,KAAKL,YAAY;QACvC,iEAAiE;QACjE,kBAAkB;QAClB,OAAOE;IACT;IAEA,4EAA4E;IAC5E,2EAA2E;IAC3E,+DAA+D;IAC/D,EAAE;IACF,6EAA6E;IAC7E,6DAA6D;IAE7D,2EAA2E;IAC3E,eAAe;IACf,MAAMM,iBAAqC;QACzCH,MAAML;QACNM,UAAUL;QACVM,MAAM;IACR;IAEA,2EAA2E;IAC3E,6EAA6E;IAC7E,mDAAmD;IACnD,IAAIE,IAAI;IACR,IAAIC,WAAsCR;IAC1C,IAAIS,cAAkCH;IACtC,MAAOE,aAAa,QAAQD,IAAId,qBAAsB;QACpD,IAAIe,SAASJ,QAAQ,KAAKL,gBAAgB;YACxC,yEAAyE;YACzE,yEAAyE;YACzE,0EAA0E;YAC1E,sEAAsE;YACtE,sEAAsE;YACtE,0EAA0E;YAC1E,0DAA0D;YAC1DU,YAAYJ,IAAI,GAAGG,SAASH,IAAI;YAChC;QACF,OAAO;YACL,6CAA6C;YAC7CE;YACA,MAAMG,QAA4B;gBAChCP,MAAMK,SAASL,IAAI;gBACnBC,UAAUI,SAASJ,QAAQ;gBAC3BC,MAAM;YACR;YACAI,YAAYJ,IAAI,GAAGK;YACnBD,cAAcC;QAChB;QACAF,WAAWA,SAASH,IAAI;IAC1B;IAEAJ,mBAAmBK;IACnB,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3282, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/next-devtools/userspace/app/segment-explorer-node.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactNode } from 'react'\nimport {\n  useState,\n  createContext,\n  useContext,\n  use,\n  useMemo,\n  useCallback,\n} from 'react'\nimport { useLayoutEffect } from 'react'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport { notFound } from '../../../client/components/not-found'\n\nexport type SegmentBoundaryType =\n  | 'not-found'\n  | 'error'\n  | 'loading'\n  | 'global-error'\n\nexport const SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE =\n  'NEXT_DEVTOOLS_SIMULATED_ERROR'\n\nexport type SegmentNodeState = {\n  type: string\n  pagePath: string\n  boundaryType: string | null\n  setBoundaryType: (type: SegmentBoundaryType | null) => void\n}\n\nfunction SegmentTrieNode({\n  type,\n  pagePath,\n}: {\n  type: string\n  pagePath: string\n}): React.ReactNode {\n  const { boundaryType, setBoundaryType } = useSegmentState()\n  const nodeState: SegmentNodeState = useMemo(() => {\n    return {\n      type,\n      pagePath,\n      boundaryType,\n      setBoundaryType,\n    }\n  }, [type, pagePath, boundaryType, setBoundaryType])\n\n  // Use `useLayoutEffect` to ensure the state is updated during suspense.\n  // `useEffect` won't work as the state is preserved during suspense.\n  useLayoutEffect(() => {\n    dispatcher.segmentExplorerNodeAdd(nodeState)\n    return () => {\n      dispatcher.segmentExplorerNodeRemove(nodeState)\n    }\n  }, [nodeState])\n\n  return null\n}\n\nfunction NotFoundSegmentNode(): React.ReactNode {\n  notFound()\n}\n\nfunction ErrorSegmentNode(): React.ReactNode {\n  throw new Error(SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE)\n}\n\nconst forever = new Promise(() => {})\nfunction LoadingSegmentNode(): React.ReactNode {\n  use(forever)\n  return null\n}\n\nexport function SegmentViewStateNode({ page }: { page: string }) {\n  useLayoutEffect(() => {\n    dispatcher.segmentExplorerUpdateRouteState(page)\n    return () => {\n      dispatcher.segmentExplorerUpdateRouteState('')\n    }\n  }, [page])\n  return null\n}\n\nexport function SegmentBoundaryTriggerNode() {\n  const { boundaryType } = useSegmentState()\n  let segmentNode: React.ReactNode = null\n  if (boundaryType === 'loading') {\n    segmentNode = <LoadingSegmentNode />\n  } else if (boundaryType === 'not-found') {\n    segmentNode = <NotFoundSegmentNode />\n  } else if (boundaryType === 'error') {\n    segmentNode = <ErrorSegmentNode />\n  }\n  return segmentNode\n}\n\nexport function SegmentViewNode({\n  type,\n  pagePath,\n  children,\n}: {\n  type: string\n  pagePath: string\n  children?: ReactNode\n}): React.ReactNode {\n  const segmentNode = (\n    <SegmentTrieNode key={type} type={type} pagePath={pagePath} />\n  )\n\n  return (\n    <>\n      {segmentNode}\n      {children}\n    </>\n  )\n}\n\nconst SegmentStateContext = createContext<{\n  boundaryType: SegmentBoundaryType | null\n  setBoundaryType: (type: SegmentBoundaryType | null) => void\n}>({\n  boundaryType: null,\n  setBoundaryType: () => {},\n})\n\nexport function SegmentStateProvider({ children }: { children: ReactNode }) {\n  const [boundaryType, setBoundaryType] = useState<SegmentBoundaryType | null>(\n    null\n  )\n\n  const [errorBoundaryKey, setErrorBoundaryKey] = useState(0)\n  const reloadBoundary = useCallback(\n    () => setErrorBoundaryKey((prev) => prev + 1),\n    []\n  )\n\n  const setBoundaryTypeAndReload = useCallback(\n    (type: SegmentBoundaryType | null) => {\n      if (type === null) {\n        reloadBoundary()\n      }\n      setBoundaryType(type)\n    },\n    [reloadBoundary]\n  )\n\n  return (\n    <SegmentStateContext.Provider\n      key={errorBoundaryKey}\n      value={{\n        boundaryType,\n        setBoundaryType: setBoundaryTypeAndReload,\n      }}\n    >\n      {children}\n    </SegmentStateContext.Provider>\n  )\n}\n\nexport function useSegmentState() {\n  return useContext(SegmentStateContext)\n}\n"], "names": ["useState", "createContext", "useContext", "use", "useMemo", "useCallback", "useLayoutEffect", "dispatcher", "notFound", "SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE", "SegmentTrieNode", "type", "pagePath", "boundaryType", "setBoundaryType", "useSegmentState", "nodeState", "segmentExplorerNodeAdd", "segmentExplorerNodeRemove", "NotFoundSegmentNode", "ErrorSegmentNode", "Error", "forever", "Promise", "LoadingSegmentNode", "SegmentViewStateNode", "page", "segmentExplorerUpdateRouteState", "SegmentBoundaryTriggerNode", "segmentNode", "SegmentViewNode", "children", "SegmentStateContext", "SegmentStateProvider", "error<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "setError<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reloadBoundary", "prev", "setBoundaryTypeAndReload", "Provider", "value"], "mappings": ";;;;;;;;;;;;;;;AAGA,SACEA,QAAQ,EACRC,aAAa,EACbC,UAAU,EACVC,GAAG,EACHC,OAAO,EACPC,WAAW,QACN,QAAO;AAEd,SAASE,UAAU,QAAQ,mCAAkC;AAC7D,SAASC,QAAQ,QAAQ,uCAAsC;AAb/D;;;;;;AAqBO,MAAMC,2CACX,gCAA+B;AASjC,SAASC,gBAAgB,KAMxB;IANwB,IAAA,EACvBC,IAAI,EACJC,QAAQ,EAIT,GANwB;IAOvB,MAAM,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAGC;IAC1C,MAAMC,YAA8BZ,oNAAAA,EAAQ;QAC1C,OAAO;YACLO;YACAC;YACAC;YACAC;QACF;IACF,GAAG;QAACH;QAAMC;QAAUC;QAAcC;KAAgB;IAElD,wEAAwE;IACxE,oEAAoE;QACpER,wNAAAA,EAAgB;QACdC,mLAAAA,CAAWU,sBAAsB,CAACD;QAClC,OAAO;YACLT,mLAAAA,CAAWW,yBAAyB,CAACF;QACvC;IACF,GAAG;QAACA;KAAU;IAEd,OAAO;AACT;AAEA,SAASG;QACPX,uLAAAA;AACF;AAEA,SAASY;IACP,MAAM,OAAA,cAAmD,CAAnD,IAAIC,MAAMZ,2CAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAkD;AAC1D;AAEA,MAAMa,UAAU,IAAIC,QAAQ,KAAO;AACnC,SAASC;QACPrB,4MAAAA,EAAImB;IACJ,OAAO;AACT;AAEO,SAASG,qBAAqB,KAA0B;IAA1B,IAAA,EAAEC,IAAI,EAAoB,GAA1B;QACnCpB,wNAAAA,EAAgB;QACdC,mLAAAA,CAAWoB,+BAA+B,CAACD;QAC3C,OAAO;YACLnB,mLAAAA,CAAWoB,+BAA+B,CAAC;QAC7C;IACF,GAAG;QAACD;KAAK;IACT,OAAO;AACT;AAEO,SAASE;IACd,MAAM,EAAEf,YAAY,EAAE,GAAGE;IACzB,IAAIc,cAA+B;IACnC,IAAIhB,iBAAiB,WAAW;QAC9BgB,cAAAA,WAAAA,OAAc,8NAAA,EAACL,oBAAAA,CAAAA;IACjB,OAAO,IAAIX,iBAAiB,aAAa;QACvCgB,cAAAA,WAAAA,OAAc,8NAAA,EAACV,qBAAAA,CAAAA;IACjB,OAAO,IAAIN,iBAAiB,SAAS;QACnCgB,cAAAA,WAAAA,OAAc,8NAAA,EAACT,kBAAAA,CAAAA;IACjB;IACA,OAAOS;AACT;AAEO,SAASC,gBAAgB,KAQ/B;IAR+B,IAAA,EAC9BnB,IAAI,EACJC,QAAQ,EACRmB,QAAQ,EAKT,GAR+B;IAS9B,MAAMF,cAAAA,WAAAA,OACJ,8NAAA,EAACnB,iBAAAA;QAA2BC,MAAMA;QAAMC,UAAUA;OAA5BD;IAGxB,OAAA,WAAA,OACE,+NAAA,EAAA,mOAAA,EAAA;;YACGkB;YACAE;;;AAGP;AAEA,MAAMC,sBAAAA,WAAAA,OAAsB/B,sNAAAA,EAGzB;IACDY,cAAc;IACdC,iBAAiB,KAAO;AAC1B;AAEO,SAASmB,qBAAqB,KAAqC;IAArC,IAAA,EAAEF,QAAQ,EAA2B,GAArC;IACnC,MAAM,CAAClB,cAAcC,gBAAgB,GAAGd,qNAAAA,EACtC;IAGF,MAAM,CAACkC,kBAAkBC,oBAAoB,OAAGnC,iNAAAA,EAAS;IACzD,MAAMoC,qBAAiB/B,oNAAAA,EACrB,IAAM8B,oBAAoB,CAACE,OAASA,OAAO,IAC3C,EAAE;IAGJ,MAAMC,+BAA2BjC,oNAAAA,EAC/B,CAACM;QACC,IAAIA,SAAS,MAAM;YACjByB;QACF;QACAtB,gBAAgBH;IAClB,GACA;QAACyB;KAAe;IAGlB,OAAA,WAAA,OACE,8NAAA,EAACJ,oBAAoBO,QAAQ,EAAA;QAE3BC,OAAO;YACL3B;YACAC,iBAAiBwB;QACnB;kBAECP;OANIG;AASX;AAEO,SAASnB;IACd,WAAOb,mNAAAA,EAAW8B;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3419, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/layout-router.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  <PERSON>ache<PERSON><PERSON>,\n  LazyCacheNode,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport type { ErrorComponent } from './error-boundary'\nimport {\n  ACTION_SERVER_PATCH,\n  type FocusAndScrollRef,\n} from './router-reducer/router-reducer-types'\n\nimport React, {\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { disableSmoothScrollDuringRouteTransition } from '../../shared/lib/router/utils/disable-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { useRouterBFCache, type RouterBFCacheEntry } from './bfcache'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\n\nconst Activity = process.env.__NEXT_ROUTER_BF_CACHE\n  ? (require('react') as typeof import('react')).unstable_Activity\n  : null!\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        'Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:',\n        element\n      )\n    }\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      disableSmoothScrollDuringRouteTransition(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `disableSmoothScrollDuringRouteTransition`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  cacheNode,\n  url,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  cacheNode: CacheNode\n  url: string\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Check if there's already a pending request.\n    let lazyData = cacheNode.lazyData\n    if (lazyData === null) {\n      /**\n       * Router state with refetch marker added\n       */\n      // TODO-APP: remove ''\n      const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n      const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n      const navigatedAt = Date.now()\n      cacheNode.lazyData = lazyData = fetchServerResponse(\n        new URL(url, location.origin),\n        {\n          flightRouterState: refetchTree,\n          nextUrl: includeNextUrl ? context.nextUrl : null,\n        }\n      ).then((serverResponse) => {\n        startTransition(() => {\n          dispatchAppRouterAction({\n            type: ACTION_SERVER_PATCH,\n            previousTree: fullTree,\n            serverResponse,\n            navigatedAt,\n          })\n        })\n\n        return serverResponse\n      })\n\n      // Suspend while waiting for lazyData to resolve\n      use(lazyData)\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n      }}\n    >\n      {resolvedRsc}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  loading,\n  children,\n}: {\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n  segmentViewBoundaries,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n  segmentViewBoundaries?: React.ReactNode\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const { parentTree, parentCacheNode, parentSegmentPath, url } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n  const parentTreeSegment = parentTree[0]\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const activeTree = parentTree[1][parallelRouterKey]\n  const activeSegment = activeTree[0]\n  const activeStateKey = createRouterCacheKey(activeSegment, true) // no search params\n\n  // At each level of the route tree, not only do we render the currently\n  // active segment — we also render the last N segments that were active at\n  // this level inside a hidden <Activity> boundary, to preserve their state\n  // if or when the user navigates to them again.\n  //\n  // bfcacheEntry is a linked list of FlightRouterStates.\n  let bfcacheEntry: RouterBFCacheEntry | null = useRouterBFCache(\n    activeTree,\n    activeStateKey\n  )\n  let children: Array<React.ReactNode> = []\n  do {\n    const tree = bfcacheEntry.tree\n    const stateKey = bfcacheEntry.stateKey\n    const segment = tree[0]\n    const cacheKey = createRouterCacheKey(segment)\n\n    // Read segment path from the parallel router cache node.\n    let cacheNode = segmentMap.get(cacheKey)\n    if (cacheNode === undefined) {\n      // When data is not available during rendering client-side we need to fetch\n      // it from the server.\n      const newLazyCacheNode: LazyCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      }\n\n      // Flight data fetch kicked off during render and put into the cache.\n      cacheNode = newLazyCacheNode\n      segmentMap.set(cacheKey, newLazyCacheNode)\n    }\n\n    /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n      - When gracefully degrade for bots, skip rendering error boundary.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n    let segmentBoundaryTriggerNode: React.ReactNode = null\n    let segmentViewStateNode: React.ReactNode = null\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      process.env.__NEXT_DEVTOOL_SEGMENT_EXPLORER\n    ) {\n      const { SegmentBoundaryTriggerNode, SegmentViewStateNode } =\n        require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n\n      const pagePrefix = normalizeAppPath(url)\n      segmentViewStateNode = (\n        <SegmentViewStateNode key={pagePrefix} page={pagePrefix} />\n      )\n\n      segmentBoundaryTriggerNode = (\n        <>\n          <SegmentBoundaryTriggerNode />\n        </>\n      )\n    }\n\n    // TODO: The loading module data for a segment is stored on the parent, then\n    // applied to each of that parent segment's parallel route slots. In the\n    // simple case where there's only one parallel route (the `children` slot),\n    // this is no different from if the loading module data where stored on the\n    // child directly. But I'm not sure this actually makes sense when there are\n    // multiple parallel routes. It's not a huge issue because you always have\n    // the option to define a narrower loading boundary for a particular slot. But\n    // this sort of smells like an implementation accident to me.\n    const loadingModuleData = parentCacheNode.loading\n    let child = (\n      <TemplateContext.Provider\n        key={stateKey}\n        value={\n          <ScrollAndFocusHandler segmentPath={segmentPath}>\n            <ErrorBoundary\n              errorComponent={error}\n              errorStyles={errorStyles}\n              errorScripts={errorScripts}\n            >\n              <LoadingBoundary loading={loadingModuleData}>\n                <HTTPAccessFallbackBoundary\n                  notFound={notFound}\n                  forbidden={forbidden}\n                  unauthorized={unauthorized}\n                >\n                  <RedirectBoundary>\n                    <InnerLayoutRouter\n                      url={url}\n                      tree={tree}\n                      cacheNode={cacheNode}\n                      segmentPath={segmentPath}\n                    />\n                    {segmentBoundaryTriggerNode}\n                  </RedirectBoundary>\n                </HTTPAccessFallbackBoundary>\n              </LoadingBoundary>\n            </ErrorBoundary>\n            {segmentViewStateNode}\n          </ScrollAndFocusHandler>\n        }\n      >\n        {templateStyles}\n        {templateScripts}\n        {template}\n      </TemplateContext.Provider>\n    )\n\n    if (process.env.NODE_ENV !== 'production') {\n      const { SegmentStateProvider } =\n        require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n\n      child = (\n        <SegmentStateProvider key={stateKey}>\n          {child}\n          {segmentViewBoundaries}\n        </SegmentStateProvider>\n      )\n    }\n\n    if (process.env.__NEXT_ROUTER_BF_CACHE) {\n      child = (\n        <Activity\n          key={stateKey}\n          mode={stateKey === activeStateKey ? 'visible' : 'hidden'}\n        >\n          {child}\n        </Activity>\n      )\n    }\n\n    children.push(child)\n\n    bfcacheEntry = bfcacheEntry.next\n  } while (bfcacheEntry !== null)\n\n  return children\n}\n"], "names": ["ACTION_SERVER_PATCH", "React", "useContext", "use", "startTransition", "Suspense", "useDeferredValue", "ReactDOM", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "fetchServerResponse", "unresolvedThenable", "Error<PERSON>ou<PERSON><PERSON>", "matchSegment", "disableSmoothScrollDuringRouteTransition", "RedirectBoundary", "HTTPAccessFallbackBoundary", "createRouterCache<PERSON>ey", "hasInterceptionRouteInCurrentTree", "dispatchAppRouterAction", "useRouterBFCache", "normalizeAppPath", "Activity", "process", "env", "__NEXT_ROUTER_BF_CACHE", "require", "unstable_Activity", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "hasOwnProperty", "subTree", "undefined", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "NODE_ENV", "console", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "parentElement", "localName", "nextElement<PERSON><PERSON>ling", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "Error", "InnerLayoutRouter", "tree", "cacheNode", "url", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "resolvedRsc", "then", "lazyData", "refetchTree", "includeNextUrl", "navigatedAt", "Date", "now", "URL", "location", "origin", "flightRouterState", "nextUrl", "serverResponse", "type", "previousTree", "subtree", "Provider", "value", "parentTree", "parentCacheNode", "parentSegmentPath", "LoadingBoundary", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "fallback", "OuterLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "error", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "forbidden", "unauthorized", "segmentViewBoundaries", "parentParallelRoutes", "parallelRoutes", "segmentMap", "get", "Map", "set", "parentTreeSegment", "concat", "activeTree", "activeSegment", "activeStateKey", "bfcacheEntry", "stateKey", "cache<PERSON>ey", "newLazyCacheNode", "head", "prefetchHead", "segmentBoundaryTriggerNode", "segmentViewStateNode", "__NEXT_DEVTOOL_SEGMENT_EXPLORER", "SegmentBoundaryTriggerNode", "SegmentViewStateNode", "pagePrefix", "page", "child", "errorComponent", "SegmentStateProvider", "mode", "push", "next"], "mappings": ";;;;;AAYA,SACEA,mBAAmB,QAEd,wCAAuC;AAE9C,OAAOC,SACLC,UAAU,EACVC,GAAG,EACHC,eAAe,EACfC,QAAQ,EACRC,gBAAgB,QAEX,QAAO;AACd,OAAOC,cAAc,YAAW;AAChC,SACEC,mBAAmB,EACnBC,yBAAyB,EACzBC,eAAe,QACV,qDAAoD;AAC3D,SAASC,mBAAmB,QAAQ,yCAAwC;AAC5E,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,wCAAwC,QAAQ,sDAAqD;AAC9G,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,0BAA0B,QAAQ,wCAAuC;AAClF,SAASC,oBAAoB,QAAQ,2CAA0C;AAC/E,SAASC,iCAAiC,QAAQ,mEAAkE;AACpH,SAASC,uBAAuB,QAAQ,qBAAoB;AAC5D,SAASC,gBAAgB,QAAiC,YAAW;AACrE,SAASC,gBAAgB,QAAQ,0CAAyC;AA1C1E;;;;;;;;;;;;;;;;;;AA4CA,MAAMC,WAAWC,QAAQC,GAAG,CAACC,sBAAsB,IAC9CC,QAAQ,SAAoCC,SAC7C,QAD8D;AAGlE;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,QAAIrB,gMAAAA,EAAaiB,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACK,cAAc,CAACH,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMG,UAAUR,eACdS,WACAP,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBI,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLN,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBS,KAAK,CAAC,IACxBR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,MAAMS,+DACJjC,uNAAAA,CACAiC,4DAA4D;AAE9D,4FAA4F;AAC5F;;CAEC,GACD,SAASC,YACPC,QAAgD;IAEhD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,kBAAa,OAAO;;;IAE1C,uGAAuG;IACvG,kCAAkC;IAClC,MAAMC,+BACJJ,6DAA6DC,WAAW;AAE5E;AAEA,MAAMI,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACC,QAAQ,CAACC,iBAAiBF,SAASG,QAAQ,GAAG;QACpE,IAAI1B,QAAQC,GAAG,CAAC0B,QAAQ,KAAK,WAAe;YAC1CC,QAAQC,IAAI,CACV,4FACAN;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMO,OAAOP,QAAQQ,qBAAqB;IAC1C,OAAOV,eAAeW,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBX,OAAoB,EAAEY,cAAsB;IAC1E,MAAML,OAAOP,QAAQQ,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,aAAAA,KAAAA,OAAxBC,2BACA,AACAA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE,mDADmD;AAGlG;AAMA,MAAMK,mCAAmClE,gNAAAA,CAAMmE,SAAS;IA4GtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;QAzHF,KAAA,IAAA,OAAA,IAAA,CACEN,qBAAAA,GAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAAC3C,MAAM,KAAK,KAC1C,CAACsC,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYrB,KAAK,CAAC,CAACxB,SAASiD,YAC1BnE,gMAAAA,EAAakB,SAASgD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMpB,eAAeW,kBAAkBX,YAAY;gBAEnD,IAAIA,cAAc;oBAChBoB,UAAUrB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACoB,SAAS;oBACZA,UAAUzC,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAEyC,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMtC,kBAAkBoC,SAAU;oBACtE,IAAI1D,QAAQC,GAAG,CAAC0B,QAAQ,KAAK,WAAc;4BACrC+B;wBAAJ,IAAIA,CAAAA,CAAAA,yBAAAA,QAAQG,aAAa,KAAA,OAAA,KAAA,IAArBH,uBAAuBI,SAAS,MAAK,QAAQ;wBAC/C,2FAA2F;wBAC3F,yEAAyE;wBACzE,iHAAiH;wBACnH;oBACF;oBAEA,uGAAuG;oBACvG,IAAIJ,QAAQK,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAL,UAAUA,QAAQK,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7Ed,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBX,YAAY,GAAG;gBACjCW,kBAAkBK,YAAY,GAAG,EAAE;oBAEnC/D,kPAAAA,EACE;oBACE,uEAAuE;oBACvE,IAAI+C,cAAc;;wBACdoB,QAAwBM,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc1B,SAAS2B,eAAe;oBAC5C,MAAM/B,iBAAiB8B,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAIjC,uBAAuBwB,SAAwBvB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7H8B,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAAClC,uBAAuBwB,SAAwBvB,iBAAiB;wBACnE,0EAA0E;;wBACxEuB,QAAwBM,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBrB,kBAAkBqB,cAAc;gBAClD;gBAGF,8FAA8F;gBAC9FrB,kBAAkBqB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3BZ,QAAQa,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BnB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMqB,cAAU/F,mNAAAA,EAAWO,0PAAAA;IAC3B,IAAI,CAACwF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIC,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,OAAA,WAAA,GACE,kOAAA,EAAC/B,4BAAAA;QACCU,aAAaA;QACbJ,mBAAmBwB,QAAQxB,iBAAiB;kBAE3CG;;AAGP;AAEA;;CAEC,GACD,SAASuB,kBAAkB,KAU1B;IAV0B,IAAA,EACzBC,IAAI,EACJvB,WAAW,EACXwB,SAAS,EACTC,GAAG,EAMJ,GAV0B;IAWzB,MAAML,cAAU/F,mNAAAA,EAAWO,0PAAAA;IAC3B,IAAI,CAACwF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIC,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,MAAM,EAAEE,MAAMG,QAAQ,EAAE,GAAGN;IAE3B,yDAAyD;IAEzD,4EAA4E;IAC5E,2EAA2E;IAC3E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,MAAMO,sBACJH,UAAUI,WAAW,KAAK,OAAOJ,UAAUI,WAAW,GAAGJ,UAAUK,GAAG;IAExE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,MAAMA,UAAWpG,yNAAAA,EAAiB+F,UAAUK,GAAG,EAAEF;IAEjD,wEAAwE;IACxE,2EAA2E;IAC3E,8EAA8E;IAC9E,mBAAmB;IACnB,MAAMG,cACJ,OAAOD,QAAQ,YAAYA,QAAQ,QAAQ,OAAOA,IAAIE,IAAI,KAAK,iBAC3DzG,4MAAAA,EAAIuG,OACJA;IAEN,IAAI,CAACC,aAAa;QAChB,qEAAqE;QACrE,yEAAyE;QACzE,kCAAkC;QAElC,8CAA8C;QAC9C,IAAIE,WAAWR,UAAUQ,QAAQ;QACjC,IAAIA,aAAa,MAAM;YACrB;;OAEC,GACD,sBAAsB;YACtB,MAAMC,cAAcjF,eAAe;gBAAC;mBAAOgD;aAAY,EAAE0B;YACzD,MAAMQ,qBAAiB5F,0RAAAA,EAAkCoF;YACzD,MAAMS,cAAcC,KAAKC,GAAG;YAC5Bb,UAAUQ,QAAQ,GAAGA,eAAWlG,sOAAAA,EAC9B,IAAIwG,IAAIb,KAAKc,SAASC,MAAM,GAC5B;gBACEC,mBAAmBR;gBACnBS,SAASR,iBAAiBd,QAAQsB,OAAO,GAAG;YAC9C,GACAX,IAAI,CAAC,CAACY;oBACNpH,wNAAAA,EAAgB;wBACdgB,gNAAAA,EAAwB;wBACtBqG,MAAMzH,qOAAAA;wBACN0H,cAAcnB;wBACdiB;wBACAR;oBACF;gBACF;gBAEA,OAAOQ;YACT;YAEA,gDAAgD;gBAChDrH,4MAAAA,EAAI0G;QACN;QACA,yGAAyG;QACzG,iIAAiI;YACjI1G,4MAAAA,EAAIS,2MAAAA;IACN;IAEA,yEAAyE;IACzE,MAAM+G,UACJ,kBACA,0DAD4E,oKAC5E,EAACnH,oPAAAA,CAAoBoH,QAAQ,EAAA;QAC3BC,OAAO;YACLC,YAAY1B;YACZ2B,iBAAiB1B;YACjB2B,mBAAmBnD;YAEnB,kDAAkD;YAClDyB,KAAKA;QACP;kBAECK;;IAGL,iFAAiF;IACjF,OAAOgB;AACT;AAEA;;;CAGC,GACD,SAASM,gBAAgB,KAMxB;IANwB,IAAA,EACvBC,OAAO,EACPtD,QAAQ,EAIT,GANwB;IAOvB,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,8BAA8B;IAC9B,IAAIuD;IACJ,IACE,OAAOD,YAAY,YACnBA,YAAY,QACZ,OAAQA,QAAgBtB,IAAI,KAAK,YACjC;QACA,MAAMwB,oBAAoBF;QAC1BC,wBAAoBhI,4MAAAA,EAAIiI;IAC1B,OAAO;QACLD,oBAAoBD;IACtB;IAEA,IAAIC,mBAAmB;QACrB,MAAME,aAAaF,iBAAiB,CAAC,EAAE;QACvC,MAAMG,gBAAgBH,iBAAiB,CAAC,EAAE;QAC1C,MAAMI,iBAAiBJ,iBAAiB,CAAC,EAAE;QAC3C,OAAA,WAAA,GACE,kOAAA,EAAC9H,iNAAAA,EAAAA;YACCmI,UAAAA,WAAAA,OACE,+NAAA,EAAA,mOAAA,EAAA;;oBACGF;oBACAC;oBACAF;;;sBAIJzD;;IAGP;IAEA,OAAA,WAAA,OAAO,8NAAA,EAAA,mOAAA,EAAA;kBAAGA;;AACZ;AAMe,SAAS6D,kBAAkB,KAwBzC;IAxByC,IAAA,EACxCC,iBAAiB,EACjBC,KAAK,EACLC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,qBAAqB,EAatB,GAxByC;IAyBxC,MAAMnD,cAAU/F,mNAAAA,EAAWM,oPAAAA;IAC3B,IAAI,CAACyF,SAAS;QACZ,MAAM,OAAA,cAA2D,CAA3D,IAAIC,MAAM,mDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0D;IAClE;IAEA,MAAM,EAAE4B,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,EAAE1B,GAAG,EAAE,GAAGL;IAEhE,6EAA6E;IAC7E,aAAa;IACb,MAAMoD,uBAAuBtB,gBAAgBuB,cAAc;IAC3D,IAAIC,aAAaF,qBAAqBG,GAAG,CAACd;IAC1C,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACa,YAAY;QACfA,aAAa,IAAIE;QACjBJ,qBAAqBK,GAAG,CAAChB,mBAAmBa;IAC9C;IACA,MAAMI,oBAAoB7B,UAAU,CAAC,EAAE;IACvC,MAAMjD,cACJmD,sBAAsB,OAElB,AACA,qCAAqC,iCADiC;IAEtE;QAACU;KAAkB,GACnBV,kBAAkB4B,MAAM,CAAC;QAACD;QAAmBjB;KAAkB;IAErE,8EAA8E;IAC9E,uEAAuE;IACvE,8EAA8E;IAC9E,6EAA6E;IAC7E,0DAA0D;IAC1D,EAAE;IACF,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,yBAAyB;IACzB,MAAMmB,aAAa/B,UAAU,CAAC,EAAE,CAACY,kBAAkB;IACnD,MAAMoB,gBAAgBD,UAAU,CAAC,EAAE;IACnC,MAAME,qBAAiB7I,4OAAAA,EAAqB4I,eAAe,MAAM,mBAAmB;;IAEpF,uEAAuE;IACvE,0EAA0E;IAC1E,0EAA0E;IAC1E,+CAA+C;IAC/C,EAAE;IACF,uDAAuD;IACvD,IAAIE,mBAA0C3I,0LAAAA,EAC5CwI,YACAE;IAEF,IAAInF,WAAmC,EAAE;IACzC,GAAG;QACD,MAAMwB,OAAO4D,aAAa5D,IAAI;QAC9B,MAAM6D,WAAWD,aAAaC,QAAQ;QACtC,MAAMjI,UAAUoE,IAAI,CAAC,EAAE;QACvB,MAAM8D,eAAWhJ,4OAAAA,EAAqBc;QAEtC,yDAAyD;QACzD,IAAIqE,YAAYkD,WAAWC,GAAG,CAACU;QAC/B,IAAI7D,cAAc/D,WAAW;YAC3B,2EAA2E;YAC3E,sBAAsB;YACtB,MAAM6H,mBAAkC;gBACtCtD,UAAU;gBACVH,KAAK;gBACLD,aAAa;gBACb2D,MAAM;gBACNC,cAAc;gBACdf,gBAAgB,IAAIG;gBACpBvB,SAAS;gBACTlB,aAAa,CAAC;YAChB;YAEA,qEAAqE;YACrEX,YAAY8D;YACZZ,WAAWG,GAAG,CAACQ,UAAUC;QAC3B;QAEA;;;;;;;;;EASF,GAEE,IAAIG,6BAA8C;QAClD,IAAIC,uBAAwC;QAC5C,IACE/I,QAAQC,GAAG,CAAC0B,QAAQ,KAAK,WAEzB,KADA3B,QAAQC,GAAG,CAAC+I,+BAA+B;YAE3C,MAAM,EAAEC,0BAA0B,EAAEC,oBAAoB,EAAE,GACxD/I,QAAQ;YAEV,MAAMgJ,aAAarJ,+MAAAA,EAAiBgF;YACpCiE,uBAAAA,WAAAA,OACE,8NAAA,EAACG,sBAAAA;gBAAsCE,MAAMD;eAAlBA;YAG7BL,6BAAAA,WAAAA,OACE,8NAAA,EAAA,mOAAA,EAAA;0BACE,WAAA,OAAA,8NAAA,EAACG,4BAAAA,CAAAA;;QAGP;QAEA,4EAA4E;QAC5E,wEAAwE;QACxE,2EAA2E;QAC3E,2EAA2E;QAC3E,4EAA4E;QAC5E,0EAA0E;QAC1E,8EAA8E;QAC9E,6DAA6D;QAC7D,MAAMtC,oBAAoBJ,gBAAgBG,OAAO;QACjD,IAAI2C,QAAAA,WAAAA,OACF,+NAAA,EAACnK,gPAAAA,CAAgBkH,QAAQ,EAAA;YAEvBC,OAAAA,WAAAA,OACE,+NAAA,EAAC7B,uBAAAA;gBAAsBnB,aAAaA;;sCAClC,8NAAA,EAAChE,iMAAAA,EAAAA;wBACCiK,gBAAgBnC;wBAChBC,aAAaA;wBACbC,cAAcA;kCAEd,WAAA,OAAA,8NAAA,EAACZ,iBAAAA;4BAAgBC,SAASC;sCACxB,WAAA,OAAA,8NAAA,EAAClH,4OAAAA,EAAAA;gCACCgI,UAAUA;gCACVC,WAAWA;gCACXC,cAAcA;0CAEd,WAAA,OAAA,+NAAA,EAACnI,uMAAAA,EAAAA;;0DACC,8NAAA,EAACmF,mBAAAA;4CACCG,KAAKA;4CACLF,MAAMA;4CACNC,WAAWA;4CACXxB,aAAaA;;wCAEdyF;;;;;;oBAKRC;;;;gBAIJzB;gBACAC;gBACAC;;WAhCIiB;QAoCT,IAAIzI,QAAQC,GAAG,CAAC0B,QAAQ,KAAK,WAAc;YACzC,MAAM,EAAE4H,oBAAoB,EAAE,GAC5BpJ,QAAQ;YAEVkJ,QAAAA,WAAAA,OACE,+NAAA,EAACE,sBAAAA;;oBACEF;oBACAzB;;eAFwBa;QAK/B;QAEA,IAAIzI,QAAQC,GAAG,CAACC,sBAAsB,EAAE;;QAWxCkD,SAASqG,IAAI,CAACJ;QAEdb,eAAeA,aAAakB,IAAI;IAClC,QAASlB,iBAAiB,KAAK;IAE/B,OAAOpF;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3953, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "TemplateContext", "RenderFromTemplateContext", "children"], "mappings": ";;;;;AAEA,OAAOA,SAASC,UAAU,QAAkB,QAAO;AACnD,SAASC,eAAe,QAAQ,qDAAoD;AAHpF;;;;AAKe,SAASC;IACtB,MAAMC,eAAWH,mNAAAA,EAAWC,gPAAAA;IAC5B,OAAA,WAAA,OAAO,8NAAA,EAAA,mOAAA,EAAA;kBAAGE;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3974, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/server/web/spec-extension/adapters/reflect.ts"], "sourcesContent": ["export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n"], "names": ["ReflectAdapter", "get", "target", "prop", "receiver", "value", "Reflect", "bind", "set", "has", "deleteProperty"], "mappings": ";;;;AAAO,MAAMA;IACX,OAAOC,IACLC,MAAS,EACTC,IAAqB,EACrBC,QAAiB,EACZ;QACL,MAAMC,QAAQC,QAAQL,GAAG,CAACC,QAAQC,MAAMC;QACxC,IAAI,OAAOC,UAAU,YAAY;YAC/B,OAAOA,MAAME,IAAI,CAACL;QACpB;QAEA,OAAOG;IACT;IAEA,OAAOG,IACLN,MAAS,EACTC,IAAqB,EACrBE,KAAU,EACVD,QAAa,EACJ;QACT,OAAOE,QAAQE,GAAG,CAACN,QAAQC,MAAME,OAAOD;IAC1C;IAEA,OAAOK,IAAsBP,MAAS,EAAEC,IAAqB,EAAW;QACtE,OAAOG,QAAQG,GAAG,CAACP,QAAQC;IAC7B;IAEA,OAAOO,eACLR,MAAS,EACTC,IAAqB,EACZ;QACT,OAAOG,QAAQI,cAAc,CAACR,QAAQC;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4000, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/server/create-deduped-by-callsite-server-error-logger.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Cache Components is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_CACHE_COMPONENTS\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n"], "names": ["React", "errorRef", "current", "cache", "fn", "logErrorOrWarn", "process", "env", "__NEXT_CACHE_COMPONENTS", "console", "error", "warn", "flushCurrentErrorIfNew", "key", "createDedupedByCallsiteServerErrorLoggerDev", "getMessage", "logDedupedError", "args", "message", "NODE_ENV", "callStackFrames", "Error", "stack", "split", "undefined", "length"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;;AAE9B,MAAMC,WAAsC;IAAEC,SAAS;AAAK;AAE5D,iFAAiF;AACjF,MAAMC,QACJ,OAAOH,MAAMG,wMAAK,KAAK,aACnBH,MAAMG,wMAAK,GACX,CAACC,KAA+BA;AAEtC,2EAA2E;AAC3E,qEAAqE;AACrE,gBAAgB;AAChB,MAAMC,iBAAiBC,QAAQC,GAAG,CAACC,uBAAuB,GACtDC,QAAQC,KAAK,aACbD,QAAQE,IAAI;AAEhB,2CAA2C;AAC3C,wGAAwG;AACxG,MAAMC,yBAAyBT,MAC7B,AACA,CAACU,yEADyE;IAExE,IAAI;QACFR,eAAeJ,SAASC,OAAO;IACjC,SAAU;QACRD,SAASC,OAAO,GAAG;IACrB;AACF;AAcK,SAASY,4CACdC,UAAoC;IAEpC,OAAO,SAASC,gBAAgB,GAAGC,IAAU;QAC3C,MAAMC,UAAUH,cAAcE;QAE9B,IAAIX,QAAQC,GAAG,CAACY,QAAQ,KAAK,WAAc;gBACjB;YAAxB,MAAMC,kBAAAA,CAAkB,SAAA,IAAIC,QAAQC,KAAK,KAAA,OAAA,KAAA,IAAjB,OAAmBC,KAAK,CAAC;YACjD,IAAIH,oBAAoBI,aAAaJ,gBAAgBK,MAAM,GAAG,GAAG;gBAC/DpB,eAAea;YACjB,OAAO;gBACL,SAAS;gBACT,oBAAoB;gBACpB,uCAAuC;gBACvC,wBAAwB;gBACxB,iEAAiE;gBACjE,MAAML,MAAMO,eAAe,CAAC,EAAE;gBAC9BnB,SAASC,OAAO,GAAGgB;gBACnBN,uBAAuBC;YACzB;QACF,OAAO;;IAGT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4050, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/shared/lib/utils/reflect-utils.ts"], "sourcesContent": ["// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport function describeStringPropertyAccess(target: string, prop: string) {\n  if (isDefinitelyAValidIdentifier.test(prop)) {\n    return `\\`${target}.${prop}\\``\n  }\n  return `\\`${target}[${JSON.stringify(prop)}]\\``\n}\n\nexport function describeHasCheckingStringProperty(\n  target: string,\n  prop: string\n) {\n  const stringifiedProp = JSON.stringify(prop)\n  return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`\n}\n\nexport const wellKnownProperties = new Set([\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toString',\n  'valueOf',\n  'toLocaleString',\n\n  // Promise prototype\n  // fallthrough\n  'then',\n  'catch',\n  'finally',\n\n  // React Promise extension\n  // fallthrough\n  'status',\n\n  // React introspection\n  'displayName',\n  '_debugInfo',\n\n  // Common tested properties\n  // fallthrough\n  'toJSON',\n  '$$typeof',\n  '__esModule',\n])\n"], "names": ["isDefinitelyAValidIdentifier", "describeStringPropertyAccess", "target", "prop", "test", "JSON", "stringify", "describeHasCheckingStringProperty", "stringifiedProp", "wellKnownProperties", "Set"], "mappings": "AAAA,6EAA6E;AAC7E,iFAAiF;AACjF,0FAA0F;AAC1F,uFAAuF;AACvF,2DAA2D;;;;;;;;;AAC3D,MAAMA,+BAA+B;AAE9B,SAASC,6BAA6BC,MAAc,EAAEC,IAAY;IACvE,IAAIH,6BAA6BI,IAAI,CAACD,OAAO;QAC3C,OAAQ,MAAID,SAAO,MAAGC,OAAK;IAC7B;IACA,OAAQ,MAAID,SAAO,MAAGG,KAAKC,SAAS,CAACH,QAAM;AAC7C;AAEO,SAASI,kCACdL,MAAc,EACdC,IAAY;IAEZ,MAAMK,kBAAkBH,KAAKC,SAAS,CAACH;IACvC,OAAQ,kBAAgBD,SAAO,OAAIM,kBAAgB,UAASA,kBAAgB,SAAMN,SAAO;AAC3F;AAEO,MAAMO,sBAAsB,IAAIC,IAAI;IACzC;IACA;IACA;IACA;IACA;IACA;IAEA,oBAAoB;IACpB,cAAc;IACd;IACA;IACA;IAEA,0BAA0B;IAC1B,cAAc;IACd;IAEA,sBAAsB;IACtB;IACA;IAEA,2BAA2B;IAC3B,cAAc;IACd;IACA;IACA;CACD,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4102, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/server/request/utils.ts"], "sourcesContent": ["import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore,\n  constructorOpt: Function\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await \"searchParams\" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  Error.captureStackTrace(error, constructorOpt)\n  workStore.invalidDynamicUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n"], "names": ["StaticGenBailoutError", "afterTaskAsyncStorage", "throwWithStaticGenerationBailoutError", "route", "expression", "throwWithStaticGenerationBailoutErrorWithDynamicError", "throwForSearchParamsAccessInUseCache", "workStore", "constructorOpt", "error", "Error", "captureStackTrace", "invalidDynamicUsageError", "isRequestAPICallableInsideAfter", "afterTaskStore", "getStore", "rootTaskSpawnPhase"], "mappings": ";;;;;;;;;;AAAA,SAASA,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,qBAAqB,QAAQ,kDAAiD;;;AAGhF,SAASC,sCACdC,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIJ,uNAAAA,CACR,CAAC,MAAM,EAAEG,MAAM,iDAAiD,EAAEC,WAAW,0HAA0H,CAAC,GADpM,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASC,sDACdF,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIJ,uNAAAA,CACR,CAAC,MAAM,EAAEG,MAAM,4EAA4E,EAAEC,WAAW,0HAA0H,CAAC,GAD/N,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASE,qCACdC,SAAoB,EACpBC,cAAwB;IAExB,MAAMC,QAAQ,OAAA,cAEb,CAFa,IAAIC,MAChB,CAAC,MAAM,EAAEH,UAAUJ,KAAK,CAAC,uXAAuX,CAAC,GADrY,qBAAA;eAAA;oBAAA;sBAAA;IAEd;IAEAO,MAAMC,iBAAiB,CAACF,OAAOD;IAC/BD,UAAUK,wBAAwB,KAAKH;IAEvC,MAAMA;AACR;AAEO,SAASI;IACd,MAAMC,iBAAiBb,8SAAAA,CAAsBc,QAAQ;IACrD,OAAOD,CAAAA,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBE,kBAAkB,MAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/server/request/search-params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n  annotateDynamicAccess,\n  trackSynchronousRequestDataAccessInDev,\n  delayUntilRuntimeStage,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type PrerenderStoreModern,\n  type PrerenderStoreModernRuntime,\n  type StaticPrerenderStore,\n  throwInvariantForMissingStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  throwWithStaticGenerationBailoutErrorWithDynamicError,\n  throwForSearchParamsAccessInUseCache,\n} from './utils'\n\nexport type SearchParams = { [key: string]: string | string[] | undefined }\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedSearchParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { searchParams: Promise<{ foo: string }> }\n *\n * export default async function Page(props: Props) {\n *  const { searchParams } = (props.searchParams as unknown as UnsafeUnwrappedSearchParams<typeof props.searchParams>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedSearchParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createSearchParamsFromClient(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderSearchParams(workStore, workUnitStore)\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createSearchParamsFromClient should not be called in a runtime prerender.'\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createSearchParamsFromClient should not be called in cache contexts.'\n        )\n      case 'request':\n        return createRenderSearchParams(underlyingSearchParams, workStore)\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata =\n  createServerSearchParamsForServerPage\n\nexport function createServerSearchParamsForServerPage(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderSearchParams(workStore, workUnitStore)\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerSearchParamsForServerPage should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderSearchParams(\n          underlyingSearchParams,\n          workUnitStore\n        )\n      case 'request':\n        return createRenderSearchParams(underlyingSearchParams, workStore)\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createPrerenderSearchParamsForClientPage(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n        // We're prerendering in a mode that aborts (cacheComponents) and should stall\n        // the promise to ensure the RSC side is considered dynamic\n        return makeHangingPromise(\n          workUnitStore.renderSignal,\n          workStore.route,\n          '`searchParams`'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createPrerenderSearchParamsForClientPage should not be called in a runtime prerender.'\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createPrerenderSearchParamsForClientPage should not be called in cache contexts.'\n        )\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'request':\n        return Promise.resolve({})\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nfunction createStaticPrerenderSearchParams(\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client':\n      // We are in a cacheComponents (PPR or otherwise) prerender\n      return makeHangingSearchParams(workStore, prerenderStore)\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // We are in a legacy static generation and need to interrupt the\n      // prerender when search params are accessed.\n      return makeErroringExoticSearchParams(workStore, prerenderStore)\n    default:\n      return prerenderStore satisfies never\n  }\n}\n\nfunction createRuntimePrerenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workUnitStore: PrerenderStoreModernRuntime\n): Promise<SearchParams> {\n  return delayUntilRuntimeStage(\n    workUnitStore,\n    process.env.__NEXT_CACHE_COMPONENTS\n      ? makeUntrackedSearchParams(underlyingSearchParams)\n      : makeUntrackedExoticSearchParams(underlyingSearchParams)\n  )\n}\n\nfunction createRenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      // Semantically we only need the dev tracking when running in `next dev`\n      // but since you would never use next dev with production NODE_ENV we use this\n      // as a proxy so we can statically exclude this code from production builds.\n      if (process.env.__NEXT_CACHE_COMPONENTS) {\n        return makeUntrackedSearchParamsWithDevWarnings(\n          underlyingSearchParams,\n          workStore\n        )\n      }\n\n      return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n        underlyingSearchParams,\n        workStore\n      )\n    } else {\n      if (process.env.__NEXT_CACHE_COMPONENTS) {\n        return makeUntrackedSearchParams(underlyingSearchParams)\n      }\n\n      return makeUntrackedExoticSearchParams(underlyingSearchParams)\n    }\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nconst CachedSearchParamsForUseCache = new WeakMap<\n  CacheLifetime,\n  Promise<SearchParams>\n>()\n\nfunction makeHangingSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(prerenderStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = makeHangingPromise<SearchParams>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`searchParams`'\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        default: {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n  })\n\n  CachedSearchParams.set(prerenderStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeErroringExoticSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreLegacy | PrerenderStorePPR\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const underlyingSearchParams = {}\n  // For search params we don't construct a ReactPromise because we want to interrupt\n  // rendering on any property access that was not set from outside and so we only want\n  // to have properties like value and status if React sets them.\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no cacheComponents)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no cacheComponents)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            if (workStore.dynamicShouldError) {\n              throwWithStaticGenerationBailoutErrorWithDynamicError(\n                workStore.route,\n                expression\n              )\n            } else if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        if (workStore.dynamicShouldError) {\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        } else if (prerenderStore.type === 'prerender-ppr') {\n          // PPR Prerender (no cacheComponents)\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            prerenderStore.dynamicTracking\n          )\n        } else {\n          // Legacy Prerender\n          throwToInterruptStaticGeneration(\n            expression,\n            workStore,\n            prerenderStore\n          )\n        }\n        return false\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      if (workStore.dynamicShouldError) {\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      } else if (prerenderStore.type === 'prerender-ppr') {\n        // PPR Prerender (no cacheComponents)\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          prerenderStore.dynamicTracking\n        )\n      } else {\n        // Legacy Prerender\n        throwToInterruptStaticGeneration(expression, workStore, prerenderStore)\n      }\n    },\n  })\n\n  CachedSearchParams.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\n/**\n * This is a variation of `makeErroringExoticSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */\nexport function makeErroringSearchParamsForUseCache(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve({})\n\n  const proxiedPromise = new Proxy(promise, {\n    get: function get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it. We know it\n        // isn't a dynamic access because it can only be something that was\n        // previously written to the promise and thus not an underlying\n        // searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore, get)\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has: function has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests throw an error. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore, has)\n      }\n\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys: function ownKeys() {\n      throwForSearchParamsAccessInUseCache(workStore, ownKeys)\n    },\n  })\n\n  CachedSearchParamsForUseCache.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (!wellKnownProperties.has(prop)) {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          if (workUnitStore) {\n            trackDynamicDataInDynamicRender(workUnitStore)\n          }\n          return underlyingSearchParams[prop]\n        },\n        set(value) {\n          Object.defineProperty(promise, prop, {\n            value,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n  // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n  // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n  // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n  // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n  // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n  let promiseInitialized = false\n  const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string' && promiseInitialized) {\n        if (store.dynamicShouldError) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n        const workUnitStore = workUnitAsyncStorage.getStore()\n        if (workUnitStore) {\n          trackDynamicDataInDynamicRender(workUnitStore)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (store.dynamicShouldError) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      if (store.dynamicShouldError) {\n        const expression =\n          '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = makeDevtoolsIOAwarePromise(underlyingSearchParams)\n  promise.then(() => {\n    promiseInitialized = true\n  })\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      Object.defineProperty(promise, prop, {\n        get() {\n          return proxiedUnderlying[prop]\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (prop === 'then' && store.dynamicShouldError) {\n        const expression = '`searchParams.then`'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          syncIODev(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeDynamicallyTrackedExoticSearchParamsWithDevWarnings`, but\n// just logging the sync access without actually defining the search params on\n// the promise.\nfunction makeUntrackedSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n  const promise = makeDevtoolsIOAwarePromise(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      warnForIncompleteEnumeration(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createSearchAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createSearchAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin or well-known property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["ReflectAdapter", "throwToInterruptStaticGeneration", "postponeWithTracking", "trackDynamicDataInDynamicRender", "annotateDynamicAccess", "trackSynchronousRequestDataAccessInDev", "delayUntilRuntimeStage", "workUnitAsyncStorage", "throwInvariantForMissingStore", "InvariantError", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "describeStringPropertyAccess", "describeHasCheckingStringProperty", "wellKnownProperties", "throwWithStaticGenerationBailoutErrorWithDynamicError", "throwForSearchParamsAccessInUseCache", "createSearchParamsFromClient", "underlyingSearchParams", "workStore", "workUnitStore", "getStore", "type", "createStaticPrerenderSearchParams", "createRenderSearchParams", "createServerSearchParamsForMetadata", "createServerSearchParamsForServerPage", "createRuntimePrerenderSearchParams", "createPrerenderSearchParamsForClientPage", "forceStatic", "Promise", "resolve", "renderSignal", "route", "prerenderStore", "makeHangingSearchParams", "makeErroringExoticSearchParams", "process", "env", "__NEXT_CACHE_COMPONENTS", "makeUntrackedSearchParams", "makeUntrackedExoticSearchParams", "NODE_ENV", "makeUntrackedSearchParamsWithDevWarnings", "makeDynamicallyTrackedExoticSearchParamsWithDevWarnings", "CachedSearchParams", "WeakMap", "CachedSearchParamsForUseCache", "cachedSearchParams", "get", "promise", "proxiedPromise", "Proxy", "target", "prop", "receiver", "Object", "hasOwn", "expression", "set", "dynamicShouldError", "dynamicTracking", "has", "ownKeys", "makeErroringSearchParamsForUseCache", "keys", "for<PERSON>ach", "defineProperty", "value", "writable", "enumerable", "configurable", "store", "proxiedProperties", "Set", "unproxiedProperties", "promiseInitialized", "proxiedUnderlying", "Reflect", "then", "push", "add", "newValue", "syncIODev", "delete", "warnForSyncAccess", "warnForIncompleteEnumeration", "missingProperties", "length", "prerenderPhase", "createSearchAccessError", "createIncompleteEnumerationError", "prefix", "Error", "describeListOfPropertyNames", "properties", "description", "i"], "mappings": ";;;;;;;;;;;;AAEA,SAASA,cAAc,QAAQ,yCAAwC;AACvE,SACEC,gCAAgC,EAChCC,oBAAoB,EACpBC,+BAA+B,EAC/BC,qBAAqB,EACrBC,sCAAsC,EACtCC,sBAAsB,QACjB,kCAAiC;AAExC,SACEC,oBAAoB,EAMpBC,6BAA6B,QACxB,iDAAgD;AACvD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,0BAA0B,EAC1BC,kBAAkB,QACb,6BAA4B;AACnC,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SACEC,4BAA4B,EAC5BC,iCAAiC,EACjCC,mBAAmB,QACd,uCAAsC;AAC7C,SACEC,qDAAqD,EACrDC,oCAAoC,QAC/B,UAAS;;;;;;;;;AAgCT,SAASC,6BACdC,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBd,2SAAAA,CAAqBe,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,kCAAkCJ,WAAWC;YACtD,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIZ,4LAAAA,CACR,8EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIA,4LAAAA,CACR,yEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOgB,yBAAyBN,wBAAwBC;YAC1D;gBACEC;QACJ;IACF;QACAb,oTAAAA;AACF;AAGO,MAAMkB,sCACXC,sCAAqC;AAEhC,SAASA,sCACdR,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBd,2SAAAA,CAAqBe,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,kCAAkCJ,WAAWC;YACtD,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIZ,4LAAAA,CACR,kFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOmB,mCACLT,wBACAE;YAEJ,KAAK;gBACH,OAAOI,yBAAyBN,wBAAwBC;YAC1D;gBACEC;QACJ;IACF;QACAb,oTAAAA;AACF;AAEO,SAASqB,yCACdT,SAAoB;IAEpB,IAAIA,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,MAAMX,gBAAgBd,2SAAAA,CAAqBe,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,8EAA8E;gBAC9E,2DAA2D;gBAC3D,WAAOZ,oMAAAA,EACLU,cAAcY,YAAY,EAC1Bb,UAAUc,KAAK,EACf;YAEJ,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIzB,4LAAAA,CACR,0FADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIA,4LAAAA,CACR,qFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOsB,QAAQC,OAAO,CAAC,CAAC;YAC1B;gBACEX;QACJ;IACF;QACAb,oTAAAA;AACF;AAEA,SAASgB,kCACPJ,SAAoB,EACpBe,cAAoC;IAEpC,IAAIf,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,OAAQG,eAAeZ,IAAI;QACzB,KAAK;QACL,KAAK;YACH,2DAA2D;YAC3D,OAAOa,wBAAwBhB,WAAWe;QAC5C,KAAK;QACL,KAAK;YACH,iEAAiE;YACjE,6CAA6C;YAC7C,OAAOE,+BAA+BjB,WAAWe;QACnD;YACE,OAAOA;IACX;AACF;AAEA,SAASP,mCACPT,sBAAoC,EACpCE,aAA0C;IAE1C,WAAOf,gNAAAA,EACLe,eACAiB,QAAQC,GAAG,CAACC,uBAAuB,GAC/BC,0BAA0BtB,AAC1BuB,gCAAgCvB;AAExC;AAEA,SAASM,yBACPN,sBAAoC,EACpCC,SAAoB;IAEpB,IAAIA,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B,OAAO;QACL,IAAIM,QAAQC,GAAG,CAACI,QAAQ,KAAK,WAAe;YAC1C,wEAAwE;YACxE,8EAA8E;YAC9E,4EAA4E;YAC5E,IAAIL,QAAQC,GAAG,CAACC,uBAAuB,EAAE;;YAOzC,OAAOK,wDACL1B,wBACAC;QAEJ,OAAO;;IAOT;AACF;AAGA,MAAM0B,qBAAqB,IAAIC;AAE/B,MAAMC,gCAAgC,IAAID;AAK1C,SAASX,wBACPhB,SAAoB,EACpBe,cAAoC;IAEpC,MAAMc,qBAAqBH,mBAAmBI,GAAG,CAACf;IAClD,IAAIc,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,cAAUxC,oMAAAA,EACdwB,eAAeF,YAAY,EAC3Bb,UAAUc,KAAK,EACf;IAGF,MAAMkB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOvD,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMI,aACJ;4BACFvD,+MAAAA,EAAsBuD,YAAYxB;wBAClC,OAAOnC,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBAAU;wBACb,MAAMG,aACJ;4BACFvD,+MAAAA,EAAsBuD,YAAYxB;wBAClC,OAAOnC,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBAEA;oBAAS;wBACP,OAAOxD,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;IACF;IAEAV,mBAAmBc,GAAG,CAACzB,gBAAgBiB;IACvC,OAAOA;AACT;AAEA,SAASf,+BACPjB,SAAoB,EACpBe,cAAwD;IAExD,MAAMc,qBAAqBH,mBAAmBI,GAAG,CAAC9B;IAClD,IAAI6B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAM9B,yBAAyB,CAAC;IAChC,mFAAmF;IACnF,qFAAqF;IACrF,+DAA+D;IAC/D,MAAMgC,UAAUpB,QAAQC,OAAO,CAACb;IAEhC,MAAMiC,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOvD,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMI,aACJ;wBACF,IAAIvC,UAAUyC,kBAAkB,EAAE;gCAChC7C,0NAAAA,EACEI,UAAUc,KAAK,EACfyB;wBAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;4BAClD,qCAAqC;gCACrCrB,8MAAAA,EACEkB,UAAUc,KAAK,EACfyB,YACAxB,eAAe2B,eAAe;wBAElC,OAAO;4BACL,mBAAmB;gCACnB7D,0NAAAA,EACE0D,YACAvC,WACAe;wBAEJ;wBACA;oBACF;gBACA,KAAK;oBAAU;wBACb,MAAMwB,aACJ;wBACF,IAAIvC,UAAUyC,kBAAkB,EAAE;gCAChC7C,0NAAAA,EACEI,UAAUc,KAAK,EACfyB;wBAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;4BAClD,qCAAqC;gCACrCrB,8MAAAA,EACEkB,UAAUc,KAAK,EACfyB,YACAxB,eAAe2B,eAAe;wBAElC,OAAO;4BACL,mBAAmB;gCACnB7D,0NAAAA,EACE0D,YACAvC,WACAe;wBAEJ;wBACA;oBACF;gBACA;oBAAS;wBACP,IAAI,OAAOoB,SAAS,YAAY,CAACxC,wMAAAA,CAAoBgD,GAAG,CAACR,OAAO;4BAC9D,MAAMI,iBAAa9C,iNAAAA,EACjB,gBACA0C;4BAEF,IAAInC,UAAUyC,kBAAkB,EAAE;oCAChC7C,0NAAAA,EACEI,UAAUc,KAAK,EACfyB;4BAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;gCAClD,qCAAqC;oCACrCrB,8MAAAA,EACEkB,UAAUc,KAAK,EACfyB,YACAxB,eAAe2B,eAAe;4BAElC,OAAO;gCACL,mBAAmB;oCACnB7D,0NAAAA,EACE0D,YACAvC,WACAe;4BAEJ;wBACF;wBACA,OAAOnC,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,8EAA8E;YAC9E,wFAAwF;YACxF,8FAA8F;YAC9F,kEAAkE;YAClE,IAAI,OAAOA,SAAS,UAAU;gBAC5B,MAAMI,iBAAa7C,sNAAAA,EACjB,gBACAyC;gBAEF,IAAInC,UAAUyC,kBAAkB,EAAE;wBAChC7C,0NAAAA,EACEI,UAAUc,KAAK,EACfyB;gBAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;oBAClD,qCAAqC;wBACrCrB,8MAAAA,EACEkB,UAAUc,KAAK,EACfyB,YACAxB,eAAe2B,eAAe;gBAElC,OAAO;oBACL,mBAAmB;wBACnB7D,0NAAAA,EACE0D,YACAvC,WACAe;gBAEJ;gBACA,OAAO;YACT;YACA,OAAOnC,kNAAAA,CAAe+D,GAAG,CAACT,QAAQC;QACpC;QACAS;YACE,MAAML,aACJ;YACF,IAAIvC,UAAUyC,kBAAkB,EAAE;oBAChC7C,0NAAAA,EACEI,UAAUc,KAAK,EACfyB;YAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;gBAClD,qCAAqC;oBACrCrB,8MAAAA,EACEkB,UAAUc,KAAK,EACfyB,YACAxB,eAAe2B,eAAe;YAElC,OAAO;gBACL,mBAAmB;oBACnB7D,0NAAAA,EAAiC0D,YAAYvC,WAAWe;YAC1D;QACF;IACF;IAEAW,mBAAmBc,GAAG,CAACxC,WAAWgC;IAClC,OAAOA;AACT;AAOO,SAASa,oCACd7C,SAAoB;IAEpB,MAAM6B,qBAAqBD,8BAA8BE,GAAG,CAAC9B;IAC7D,IAAI6B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUpB,QAAQC,OAAO,CAAC,CAAC;IAEjC,MAAMoB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAK,SAASA,IAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACtC,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,wEAAwE;gBACxE,mEAAmE;gBACnE,+DAA+D;gBAC/D,oBAAoB;gBACpB,OAAOvD,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,IACE,OAAOD,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACxC,wMAAAA,CAAoBgD,GAAG,CAACR,KAAI,GACjD;oBACAtC,yMAAAA,EAAqCG,WAAW8B;YAClD;YAEA,OAAOlD,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAO,KAAK,SAASA,IAAIT,MAAM,EAAEC,IAAI;YAC5B,8EAA8E;YAC9E,uFAAuF;YACvF,8FAA8F;YAC9F,kEAAkE;YAClE,IACE,OAAOA,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACxC,wMAAAA,CAAoBgD,GAAG,CAACR,KAAI,GACjD;oBACAtC,yMAAAA,EAAqCG,WAAW2C;YAClD;YAEA,OAAO/D,kNAAAA,CAAe+D,GAAG,CAACT,QAAQC;QACpC;QACAS,SAAS,SAASA;gBAChB/C,yMAAAA,EAAqCG,WAAW4C;QAClD;IACF;IAEAhB,8BAA8BY,GAAG,CAACxC,WAAWgC;IAC7C,OAAOA;AACT;AAEA,SAASV,gCACPvB,sBAAoC;IAEpC,MAAM8B,qBAAqBH,mBAAmBI,GAAG,CAAC/B;IAClD,IAAI8B,oBAAoB;QACtB,OAAOA;IACT;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUpB,QAAQC,OAAO,CAACb;IAChC2B,mBAAmBc,GAAG,CAACzC,wBAAwBgC;IAE/CM,OAAOS,IAAI,CAAC/C,wBAAwBgD,OAAO,CAAC,CAACZ;QAC3C,IAAI,CAACxC,wMAAAA,CAAoBgD,GAAG,CAACR,OAAO;YAClCE,OAAOW,cAAc,CAACjB,SAASI,MAAM;gBACnCL;oBACE,MAAM7B,gBAAgBd,2SAAAA,CAAqBe,QAAQ;oBACnD,IAAID,eAAe;4BACjBlB,yNAAAA,EAAgCkB;oBAClC;oBACA,OAAOF,sBAAsB,CAACoC,KAAK;gBACrC;gBACAK,KAAIS,KAAK;oBACPZ,OAAOW,cAAc,CAACjB,SAASI,MAAM;wBACnCc;wBACAC,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,OAAOrB;AACT;AAEA,SAASV,0BACPtB,sBAAoC;IAEpC,MAAM8B,qBAAqBH,mBAAmBI,GAAG,CAAC/B;IAClD,IAAI8B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUpB,QAAQC,OAAO,CAACb;IAChC2B,mBAAmBc,GAAG,CAACzC,wBAAwBgC;IAE/C,OAAOA;AACT;AAEA,SAASN,wDACP1B,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAMxB,qBAAqBH,mBAAmBI,GAAG,CAAC/B;IAClD,IAAI8B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAMyB,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,0HAA0H;IAC1H,uIAAuI;IACvI,wIAAwI;IACxI,8IAA8I;IAC9I,6IAA6I;IAC7I,+GAA+G;IAC/G,IAAIC,qBAAqB;IACzB,MAAMC,oBAAoB,IAAIzB,MAAMlC,wBAAwB;QAC1D+B,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,YAAYsB,oBAAoB;gBAClD,IAAIJ,MAAMZ,kBAAkB,EAAE;oBAC5B,MAAMF,iBAAa9C,iNAAAA,EAA6B,gBAAgB0C;wBAChEvC,0NAAAA,EACEyD,MAAMvC,KAAK,EACXyB;gBAEJ;gBACA,MAAMtC,gBAAgBd,2SAAAA,CAAqBe,QAAQ;gBACnD,IAAID,eAAe;wBACjBlB,yNAAAA,EAAgCkB;gBAClC;YACF;YACA,OAAOrB,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IAAIkB,MAAMZ,kBAAkB,EAAE;oBAC5B,MAAMF,iBAAa7C,sNAAAA,EACjB,gBACAyC;wBAEFvC,0NAAAA,EACEyD,MAAMvC,KAAK,EACXyB;gBAEJ;YACF;YACA,OAAOoB,QAAQhB,GAAG,CAACT,QAAQC;QAC7B;QACAS,SAAQV,MAAM;YACZ,IAAImB,MAAMZ,kBAAkB,EAAE;gBAC5B,MAAMF,aACJ;oBACF3C,0NAAAA,EACEyD,MAAMvC,KAAK,EACXyB;YAEJ;YACA,OAAOoB,QAAQf,OAAO,CAACV;QACzB;IACF;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAMH,cAAUzC,4MAAAA,EAA2BS;IAC3CgC,QAAQ6B,IAAI,CAAC;QACXH,qBAAqB;IACvB;IAEApB,OAAOS,IAAI,CAAC/C,wBAAwBgD,OAAO,CAAC,CAACZ;QAC3C,IAAIxC,wMAAAA,CAAoBgD,GAAG,CAACR,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEqB,oBAAoBK,IAAI,CAAC1B;QAC3B,OAAO;YACLmB,kBAAkBQ,GAAG,CAAC3B;YACtBE,OAAOW,cAAc,CAACjB,SAASI,MAAM;gBACnCL;oBACE,OAAO4B,iBAAiB,CAACvB,KAAK;gBAChC;gBACAK,KAAIuB,QAAQ;oBACV1B,OAAOW,cAAc,CAACjB,SAASI,MAAM;wBACnCc,OAAOc;wBACPb,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,MAAMpB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAID,SAAS,UAAUkB,MAAMZ,kBAAkB,EAAE;gBAC/C,MAAMF,aAAa;oBACnB3C,0NAAAA,EACEyD,MAAMvC,KAAK,EACXyB;YAEJ;YACA,IAAI,OAAOJ,SAAS,UAAU;gBAC5B,IACE,CAACxC,wMAAAA,CAAoBgD,GAAG,CAACR,SACxBmB,CAAAA,kBAAkBX,GAAG,CAACR,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BwB,QAAQhB,GAAG,CAACT,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,iBAAa9C,iNAAAA,EAA6B,gBAAgB0C;oBAChE6B,UAAUX,MAAMvC,KAAK,EAAEyB;gBACzB;YACF;YACA,OAAO3D,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAI,KAAIN,MAAM,EAAEC,IAAI,EAAEc,KAAK,EAAEb,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BmB,kBAAkBW,MAAM,CAAC9B;YAC3B;YACA,OAAOwB,QAAQnB,GAAG,CAACN,QAAQC,MAAMc,OAAOb;QAC1C;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACxC,wMAAAA,CAAoBgD,GAAG,CAACR,SACxBmB,CAAAA,kBAAkBX,GAAG,CAACR,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BwB,QAAQhB,GAAG,CAACT,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,iBAAa7C,sNAAAA,EACjB,gBACAyC;oBAEF6B,UAAUX,MAAMvC,KAAK,EAAEyB;gBACzB;YACF;YACA,OAAOoB,QAAQhB,GAAG,CAACT,QAAQC;QAC7B;QACAS,SAAQV,MAAM;YACZ,MAAMK,aAAa;YACnByB,UAAUX,MAAMvC,KAAK,EAAEyB,YAAYiB;YACnC,OAAOG,QAAQf,OAAO,CAACV;QACzB;IACF;IAEAR,mBAAmBc,GAAG,CAACzC,wBAAwBiC;IAC/C,OAAOA;AACT;AAEA,4EAA4E;AAC5E,8EAA8E;AAC9E,eAAe;AACf,SAASR,yCACPzB,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAMxB,qBAAqBH,mBAAmBI,GAAG,CAAC/B;IAClD,IAAI8B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAMyB,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAC7C,MAAMzB,cAAUzC,4MAAAA,EAA2BS;IAE3CsC,OAAOS,IAAI,CAAC/C,wBAAwBgD,OAAO,CAAC,CAACZ;QAC3C,IAAIxC,wMAAAA,CAAoBgD,GAAG,CAACR,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEqB,oBAAoBK,IAAI,CAAC1B;QAC3B,OAAO;YACLmB,kBAAkBQ,GAAG,CAAC3B;QACxB;IACF;IAEA,MAAMH,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,CAACxC,wMAAAA,CAAoBgD,GAAG,CAACR,SACxBmB,CAAAA,kBAAkBX,GAAG,CAACR,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BwB,QAAQhB,GAAG,CAACT,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,iBAAa9C,iNAAAA,EAA6B,gBAAgB0C;oBAChE+B,kBAAkBb,MAAMvC,KAAK,EAAEyB;gBACjC;YACF;YACA,OAAO3D,kNAAAA,CAAekD,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAI,KAAIN,MAAM,EAAEC,IAAI,EAAEc,KAAK,EAAEb,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BmB,kBAAkBW,MAAM,CAAC9B;YAC3B;YACA,OAAOwB,QAAQnB,GAAG,CAACN,QAAQC,MAAMc,OAAOb;QAC1C;QACAO,KAAIT,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACxC,wMAAAA,CAAoBgD,GAAG,CAACR,SACxBmB,CAAAA,kBAAkBX,GAAG,CAACR,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BwB,QAAQhB,GAAG,CAACT,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,iBAAa7C,sNAAAA,EACjB,gBACAyC;oBAEF+B,kBAAkBb,MAAMvC,KAAK,EAAEyB;gBACjC;YACF;YACA,OAAOoB,QAAQhB,GAAG,CAACT,QAAQC;QAC7B;QACAS,SAAQV,MAAM;YACZ,MAAMK,aAAa;YACnB4B,6BAA6Bd,MAAMvC,KAAK,EAAEyB,YAAYiB;YACtD,OAAOG,QAAQf,OAAO,CAACV;QACzB;IACF;IAEAR,mBAAmBc,GAAG,CAACzC,wBAAwBiC;IAC/C,OAAOA;AACT;AAEA,SAASgC,UACPlD,KAAyB,EACzByB,UAAkB,EAClB6B,iBAAiC;IAEjC,gCAAgC;IAChC,IAAIA,qBAAqBA,kBAAkBC,MAAM,GAAG,GAAG;QACrDF,6BAA6BrD,OAAOyB,YAAY6B;IAClD,OAAO;QACLF,kBAAkBpD,OAAOyB;IAC3B;IAEA,MAAMtC,gBAAgBd,2SAAAA,CAAqBe,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;gBACH,IAAIF,cAAcqE,cAAc,KAAK,MAAM;oBACzC,qEAAqE;oBACrE,qEAAqE;wBACrErF,gOAAAA,EAAuCgB;gBACzC;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEA;QACJ;IACF;AACF;AAEA,MAAMiE,wBAAoB1E,gQAAAA,EACxB+E;AAGF,MAAMJ,mCACJ3E,gQAAAA,EAA4CgF;AAE9C,SAASD,wBACPzD,KAAyB,EACzByB,UAAkB;IAElB,MAAMkC,SAAS3D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAI4D,MACT,GAAGD,OAAO,KAAK,EAAElC,WAAW,EAAE,CAAC,GAC7B,CAAC,gEAAgE,CAAC,GAClE,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASiC,iCACP1D,KAAyB,EACzByB,UAAkB,EAClB6B,iBAAgC;IAEhC,MAAMK,SAAS3D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAON,CAPM,IAAI4D,MACT,GAAGD,OAAO,KAAK,EAAElC,WAAW,EAAE,CAAC,GAC7B,CAAC,gEAAgE,CAAC,GAClE,CAAC,gEAAgE,CAAC,GAClE,CAAC,iEAAiE,CAAC,GACnE,GAAGoC,4BAA4BP,mBAAmB,EAAE,CAAC,GACrD,CAAC,8DAA8D,CAAC,GAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASO,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWP,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAIhF,4LAAAA,CACR,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,CAAC,EAAE,EAAEuF,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/B,KAAK;YACH,OAAO,CAAC,EAAE,EAAEA,UAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIC,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,WAAWP,MAAM,GAAG,GAAGS,IAAK;oBAC9CD,eAAe,CAAC,EAAE,EAAED,UAAU,CAACE,EAAE,CAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,CAAC,QAAQ,EAAED,UAAU,CAACA,WAAWP,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAOQ;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4784, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/server/request/params.ts"], "sourcesContent": ["import {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n  delayUntilRuntimeStage,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type StaticPrerenderStoreModern,\n  type StaticPrerenderStore,\n  throwInvariantForMissingStore,\n  type PrerenderStoreModernRuntime,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { dynamicAccessAsyncStorage } from '../app-render/dynamic-access-async-storage.external'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { params: Promise<{ id: string }>}\n *\n * export default async function Layout(props: Props) {\n *  const directParams = (props.params as unknown as UnsafeUnwrappedParams<typeof props.params>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createParamsFromClient should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createParamsFromClient should not be called in a runtime prerender.'\n        )\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerParamsForRoute should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderParams(underlyingParams, workUnitStore)\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerParamsForServerSegment should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderParams(underlyingParams, workUnitStore)\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params\n): Promise<Params> {\n  const workStore = workAsyncStorage.getStore()\n  if (!workStore) {\n    throw new InvariantError(\n      'Missing workStore in createPrerenderParamsForClientSegment'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams) {\n          for (let key in underlyingParams) {\n            if (fallbackParams.has(key)) {\n              // This params object has one or more fallback params, so we need\n              // to consider the awaiting of this params object \"dynamic\". Since\n              // we are in cacheComponents mode we encode this as a promise that never\n              // resolves.\n              return makeHangingPromise(\n                workUnitStore.renderSignal,\n                workStore.route,\n                '`params`'\n              )\n            }\n          }\n        }\n        break\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createPrerenderParamsForClientSegment should not be called in cache contexts.'\n        )\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'prerender-runtime':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createStaticPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<Params> {\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            // This params object has one or more fallback params, so we need\n            // to consider the awaiting of this params object \"dynamic\". Since\n            // we are in cacheComponents mode we encode this as a promise that never\n            // resolves.\n            return makeHangingParams(\n              underlyingParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            return makeErroringExoticParams(\n              underlyingParams,\n              fallbackParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-legacy':\n      break\n    default:\n      prerenderStore satisfies never\n  }\n\n  if (process.env.__NEXT_CACHE_COMPONENTS) {\n    return makeUntrackedParams(underlyingParams)\n  } else {\n    return makeUntrackedExoticParams(underlyingParams)\n  }\n}\n\nfunction createRuntimePrerenderParams(\n  underlyingParams: Params,\n  workUnitStore: PrerenderStoreModernRuntime\n): Promise<Params> {\n  return delayUntilRuntimeStage(\n    workUnitStore,\n    process.env.__NEXT_CACHE_COMPONENTS\n      ? makeUntrackedParams(underlyingParams)\n      : makeUntrackedExoticParams(underlyingParams)\n  )\n}\n\nfunction createRenderParamsInProd(underlyingParams: Params): Promise<Params> {\n  if (process.env.__NEXT_CACHE_COMPONENTS) {\n    return makeUntrackedParams(underlyingParams)\n  }\n\n  return makeUntrackedExoticParams(underlyingParams)\n}\n\nfunction createRenderParamsInDev(\n  underlyingParams: Params,\n  devFallbackParams: FallbackRouteParams | null | undefined,\n  workStore: WorkStore\n): Promise<Params> {\n  let hasFallbackParams = false\n  if (devFallbackParams) {\n    for (let key in underlyingParams) {\n      if (devFallbackParams.has(key)) {\n        hasFallbackParams = true\n        break\n      }\n    }\n  }\n  if (process.env.__NEXT_CACHE_COMPONENTS) {\n    return makeDynamicallyTrackedParamsWithDevWarnings(\n      underlyingParams,\n      hasFallbackParams,\n      workStore\n    )\n  }\n\n  return makeDynamicallyTrackedExoticParamsWithDevWarnings(\n    underlyingParams,\n    hasFallbackParams,\n    workStore\n  )\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nconst fallbackParamsProxyHandler: ProxyHandler<Promise<Params>> = {\n  get: function get(target, prop, receiver) {\n    if (prop === 'then' || prop === 'catch' || prop === 'finally') {\n      const originalMethod = ReflectAdapter.get(target, prop, receiver)\n\n      return {\n        [prop]: (...args: unknown[]) => {\n          const store = dynamicAccessAsyncStorage.getStore()\n\n          if (store) {\n            store.abortController.abort(\n              new Error(`Accessed fallback \\`params\\` during prerendering.`)\n            )\n          }\n\n          return new Proxy(\n            originalMethod.apply(target, args),\n            fallbackParamsProxyHandler\n          )\n        },\n      }[prop]\n    }\n\n    return ReflectAdapter.get(target, prop, receiver)\n  },\n}\n\nfunction makeHangingParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = new Proxy(\n    makeHangingPromise<Params>(\n      prerenderStore.renderSignal,\n      workStore.route,\n      '`params`'\n    ),\n    fallbackParamsProxyHandler\n  )\n\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeErroringExoticParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when cacheComponents is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n        Object.defineProperty(promise, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when cacheComponents is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          set(newValue) {\n            Object.defineProperty(promise, prop, {\n              value: newValue,\n              writable: true,\n              enumerable: true,\n            })\n          },\n          enumerable: true,\n          configurable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params,\n  hasFallbackParams: boolean,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = hasFallbackParams\n    ? makeDevtoolsIOAwarePromise(underlyingParams)\n    : // We don't want to force an environment transition when this params is not part of the fallback params set\n      Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeDynamicallyTrackedExoticParamsWithDevWarnings`, but just\n// logging the sync access without actually defining the params on the promise.\nfunction makeDynamicallyTrackedParamsWithDevWarnings(\n  underlyingParams: Params,\n  hasFallbackParams: boolean,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = hasFallbackParams\n    ? makeDevtoolsIOAwarePromise(underlyingParams)\n    : // We don't want to force an environment transition when this params is not part of the fallback params set\n      Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      warnForIncompleteEnumeration(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'request':\n        if (workUnitStore.prerenderPhase === true) {\n          // When we're rendering dynamically in dev, we need to advance out of\n          // the Prerender environment when we read Request data synchronously.\n          trackSynchronousRequestDataAccessInDev(workUnitStore)\n        }\n        break\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["workAsyncStorage", "ReflectAdapter", "throwToInterruptStaticGeneration", "postponeWithTracking", "trackSynchronousRequestDataAccessInDev", "delayUntilRuntimeStage", "workUnitAsyncStorage", "throwInvariantForMissingStore", "InvariantError", "describeStringPropertyAccess", "wellKnownProperties", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "dynamicAccessAsyncStorage", "createParamsFromClient", "underlyingParams", "workStore", "workUnitStore", "getStore", "type", "createStaticPrerenderParams", "process", "env", "NODE_ENV", "devFallbackParams", "createRenderParamsInDev", "createRenderParamsInProd", "createServerParamsForMetadata", "createServerParamsForServerSegment", "createServerParamsForRoute", "createRuntimePrerenderParams", "createPrerenderParamsForClientSegment", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "renderSignal", "route", "Promise", "resolve", "prerenderStore", "makeHangingParams", "makeErroringExoticParams", "__NEXT_CACHE_COMPONENTS", "makeUntrackedParams", "makeUntrackedExoticParams", "hasFallbackParams", "makeDynamicallyTrackedParamsWithDevWarnings", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "fallbackParamsProxyHandler", "get", "target", "prop", "receiver", "originalMethod", "args", "store", "abortController", "abort", "Error", "Proxy", "apply", "cachedParams", "promise", "set", "augmentedUnderlying", "Object", "keys", "for<PERSON>ach", "defineProperty", "expression", "dynamicTracking", "enumerable", "newValue", "value", "writable", "configurable", "proxiedProperties", "Set", "unproxiedProperties", "push", "add", "proxiedPromise", "syncIODev", "delete", "ownKeys", "Reflect", "warnForSyncAccess", "warnForIncompleteEnumeration", "missingProperties", "prerenderPhase", "length", "createParamsAccessError", "createIncompleteEnumerationError", "prefix", "describeListOfPropertyNames", "properties", "description", "i"], "mappings": ";;;;;;;;;;;;AAAA,SACEA,gBAAgB,QAEX,4CAA2C;AAGlD,SAASC,cAAc,QAAQ,yCAAwC;AACvE,SACEC,gCAAgC,EAChCC,oBAAoB,EACpBC,sCAAsC,EACtCC,sBAAsB,QACjB,kCAAiC;AAExC,SACEC,oBAAoB,EAKpBC,6BAA6B,QAExB,iDAAgD;AACvD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,4BAA4B,EAC5BC,mBAAmB,QACd,uCAAsC;AAC7C,SACEC,0BAA0B,EAC1BC,kBAAkB,QACb,6BAA4B;AACnC,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SAASC,yBAAyB,QAAQ,sDAAqD;;;;;;;;;;AAiCxF,SAASC,uBACdC,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBZ,2SAAAA,CAAqBa,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLL,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIV,4LAAAA,CACR,mEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIA,4LAAAA,CACR,wEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBP,cAAcO,iBAAiB;oBACzD,OAAOC,wBACLV,kBACAS,mBACAR;gBAEJ,OAAO;;YAGT;gBACEC;QACJ;IACF;QACAX,oTAAAA;AACF;AAIO,MAAMqB,gCAAgCC,mCAAkC;AAGxE,SAASC,2BACdd,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBZ,2SAAAA,CAAqBa,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLL,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIV,4LAAAA,CACR,uEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOuB,6BAA6Bf,kBAAkBE;YACxD,KAAK;gBACH,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBP,cAAcO,iBAAiB;oBACzD,OAAOC,wBACLV,kBACAS,mBACAR;gBAEJ,OAAO;;YAGT;gBACEC;QACJ;IACF;QACAX,oTAAAA;AACF;AAEO,SAASsB,mCACdb,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBZ,2SAAAA,CAAqBa,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLL,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIV,4LAAAA,CACR,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOuB,6BAA6Bf,kBAAkBE;YACxD,KAAK;gBACH,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBP,cAAcO,iBAAiB;oBACzD,OAAOC,wBACLV,kBACAS,mBACAR;gBAEJ,OAAO;;YAGT;gBACEC;QACJ;IACF;QACAX,oTAAAA;AACF;AAEO,SAASyB,sCACdhB,gBAAwB;IAExB,MAAMC,YAAYjB,uRAAAA,CAAiBmB,QAAQ;IAC3C,IAAI,CAACF,WAAW;QACd,MAAM,OAAA,cAEL,CAFK,IAAIT,4LAAAA,CACR,+DADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMU,gBAAgBZ,2SAAAA,CAAqBa,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,MAAMa,iBAAiBf,cAAcgB,mBAAmB;gBACxD,IAAID,gBAAgB;oBAClB,IAAK,IAAIE,OAAOnB,iBAAkB;wBAChC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,iEAAiE;4BACjE,kEAAkE;4BAClE,wEAAwE;4BACxE,YAAY;4BACZ,WAAOvB,oMAAAA,EACLM,cAAcmB,YAAY,EAC1BpB,UAAUqB,KAAK,EACf;wBAEJ;oBACF;gBACF;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAI9B,4LAAAA,CACR,kFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEU;QACJ;IACF;IACA,mFAAmF;IACnF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOqB,QAAQC,OAAO,CAACxB;AACzB;AAEA,SAASK,4BACPL,gBAAwB,EACxBC,SAAoB,EACpBwB,cAAoC;IAEpC,OAAQA,eAAerB,IAAI;QACzB,KAAK;QACL,KAAK;YAAoB;gBACvB,MAAMa,iBAAiBQ,eAAeP,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOnB,iBAAkB;wBAClC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,iEAAiE;4BACjE,kEAAkE;4BAClE,wEAAwE;4BACxE,YAAY;4BACZ,OAAOO,kBACL1B,kBACAC,WACAwB;wBAEJ;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YAAiB;gBACpB,MAAMR,iBAAiBQ,eAAeP,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOnB,iBAAkB;wBAClC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,OAAOQ,yBACL3B,kBACAiB,gBACAhB,WACAwB;wBAEJ;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YACH;QACF;YACEA;IACJ;IAEA,IAAInB,QAAQC,GAAG,CAACqB,uBAAuB,EAAE;;SAElC;QACL,OAAOE,0BAA0B9B;IACnC;AACF;AAEA,SAASe,6BACPf,gBAAwB,EACxBE,aAA0C;IAE1C,WAAOb,gNAAAA,EACLa,eACAI,QAAQC,GAAG,CAACqB,uBAAuB,GAC/BC,oBAAoB7B,MACpB8B,0BAA0B9B;AAElC;AAEA,SAASW,yBAAyBX,gBAAwB;IACxD,IAAIM,QAAQC,GAAG,CAACqB,uBAAuB,EAAE;;IAIzC,OAAOE,0BAA0B9B;AACnC;AAEA,SAASU,wBACPV,gBAAwB,EACxBS,iBAAyD,EACzDR,SAAoB;IAEpB,IAAI8B,oBAAoB;IACxB,IAAItB,mBAAmB;QACrB,IAAK,IAAIU,OAAOnB,iBAAkB;YAChC,IAAIS,kBAAkBW,GAAG,CAACD,MAAM;gBAC9BY,oBAAoB;gBACpB;YACF;QACF;IACF;IACA,IAAIzB,QAAQC,GAAG,CAACqB,uBAAuB,EAAE;;IAQzC,OAAOK,kDACLjC,kBACA+B,mBACA9B;AAEJ;AAGA,MAAMiC,eAAe,IAAIC;AAEzB,MAAMC,6BAA4D;IAChEC,KAAK,SAASA,IAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;QACtC,IAAID,SAAS,UAAUA,SAAS,WAAWA,SAAS,WAAW;YAC7D,MAAME,iBAAiBxD,kNAAAA,CAAeoD,GAAG,CAACC,QAAQC,MAAMC;YAExD,OAAO,CAAA;gBACL,CAACD,KAAK,EAAE,CAAC,GAAGG;oBACV,MAAMC,QAAQ7C,0TAAAA,CAA0BK,QAAQ;oBAEhD,IAAIwC,OAAO;wBACTA,MAAMC,eAAe,CAACC,KAAK,CACzB,OAAA,cAA8D,CAA9D,IAAIC,MAAM,CAAC,iDAAiD,CAAC,GAA7D,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6D;oBAEjE;oBAEA,OAAO,IAAIC,MACTN,eAAeO,KAAK,CAACV,QAAQI,OAC7BN;gBAEJ;YACF,CAAA,CAAC,CAACG,KAAK;QACT;QAEA,OAAOtD,kNAAAA,CAAeoD,GAAG,CAACC,QAAQC,MAAMC;IAC1C;AACF;AAEA,SAASd,kBACP1B,gBAAwB,EACxBC,SAAoB,EACpBwB,cAA0C;IAE1C,MAAMwB,eAAef,aAAaG,GAAG,CAACrC;IACtC,IAAIiD,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAU,IAAIH,UAClBnD,oMAAAA,EACE6B,eAAeJ,YAAY,EAC3BpB,UAAUqB,KAAK,EACf,aAEFc;IAGFF,aAAaiB,GAAG,CAACnD,kBAAkBkD;IAEnC,OAAOA;AACT;AAEA,SAASvB,yBACP3B,gBAAwB,EACxBiB,cAAmC,EACnChB,SAAoB,EACpBwB,cAAwD;IAExD,MAAMwB,eAAef,aAAaG,GAAG,CAACrC;IACtC,IAAIiD,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMG,sBAAsB;QAAE,GAAGpD,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMkD,UAAU3B,QAAQC,OAAO,CAAC4B;IAChClB,aAAaiB,GAAG,CAACnD,kBAAkBkD;IAEnCG,OAAOC,IAAI,CAACtD,kBAAkBuD,OAAO,CAAC,CAAChB;QACrC,IAAI7C,wMAAAA,CAAoB0B,GAAG,CAACmB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAItB,eAAeG,GAAG,CAACmB,OAAO;gBAC5Bc,OAAOG,cAAc,CAACJ,qBAAqBb,MAAM;oBAC/CF;wBACE,MAAMoB,iBAAahE,iNAAAA,EAA6B,UAAU8C;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,2EAA2E;wBAC3E,iCAAiC;wBACjC,IAAId,eAAerB,IAAI,KAAK,iBAAiB;4BAC3C,qCAAqC;gCACrCjB,8MAAAA,EACEc,UAAUqB,KAAK,EACfmC,YACAhC,eAAeiC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;gCACnBxE,0NAAAA,EACEuE,YACAxD,WACAwB;wBAEJ;oBACF;oBACAkC,YAAY;gBACd;gBACAN,OAAOG,cAAc,CAACN,SAASX,MAAM;oBACnCF;wBACE,MAAMoB,iBAAahE,iNAAAA,EAA6B,UAAU8C;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,2EAA2E;wBAC3E,iCAAiC;wBACjC,IAAId,eAAerB,IAAI,KAAK,iBAAiB;4BAC3C,qCAAqC;gCACrCjB,8MAAAA,EACEc,UAAUqB,KAAK,EACfmC,YACAhC,eAAeiC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;gCACnBxE,0NAAAA,EACEuE,YACAxD,WACAwB;wBAEJ;oBACF;oBACA0B,KAAIS,QAAQ;wBACVP,OAAOG,cAAc,CAACN,SAASX,MAAM;4BACnCsB,OAAOD;4BACPE,UAAU;4BACVH,YAAY;wBACd;oBACF;oBACAA,YAAY;oBACZI,cAAc;gBAChB;YACF,OAAO;;gBACHb,OAAe,CAACX,KAAK,GAAGvC,gBAAgB,CAACuC,KAAK;YAClD;QACF;IACF;IAEA,OAAOW;AACT;AAEA,SAASpB,0BAA0B9B,gBAAwB;IACzD,MAAMiD,eAAef,aAAaG,GAAG,CAACrC;IACtC,IAAIiD,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAU3B,QAAQC,OAAO,CAACxB;IAChCkC,aAAaiB,GAAG,CAACnD,kBAAkBkD;IAEnCG,OAAOC,IAAI,CAACtD,kBAAkBuD,OAAO,CAAC,CAAChB;QACrC,IAAI7C,wMAAAA,CAAoB0B,GAAG,CAACmB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;;YACHW,OAAe,CAACX,KAAK,GAAGvC,gBAAgB,CAACuC,KAAK;QAClD;IACF;IAEA,OAAOW;AACT;AAEA,SAASrB,oBAAoB7B,gBAAwB;IACnD,MAAMiD,eAAef,aAAaG,GAAG,CAACrC;IACtC,IAAIiD,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAU3B,QAAQC,OAAO,CAACxB;IAChCkC,aAAaiB,GAAG,CAACnD,kBAAkBkD;IAEnC,OAAOA;AACT;AAEA,SAASjB,kDACPjC,gBAAwB,EACxB+B,iBAA0B,EAC1BY,KAAgB;IAEhB,MAAMM,eAAef,aAAaG,GAAG,CAACrC;IACtC,IAAIiD,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAUnB,wBACZpC,4MAAAA,EAA2BK,oBAE3BuB,QAAQC,OAAO,CAACxB;IAEpB,MAAMgE,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7Cb,OAAOC,IAAI,CAACtD,kBAAkBuD,OAAO,CAAC,CAAChB;QACrC,IAAI7C,wMAAAA,CAAoB0B,GAAG,CAACmB,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClE2B,oBAAoBC,IAAI,CAAC5B;QAC3B,OAAO;YACLyB,kBAAkBI,GAAG,CAAC7B;YACpBW,OAAe,CAACX,KAAK,GAAGvC,gBAAgB,CAACuC,KAAK;QAClD;IACF;IAEA,MAAM8B,iBAAiB,IAAItB,MAAMG,SAAS;QACxCb,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,AACAyB,kBAAkB5C,GAAG,CAACmB,OACtB,0CAFuE;oBAGvE,MAAMkB,iBAAahE,iNAAAA,EAA6B,UAAU8C;oBAC1D+B,UAAU3B,MAAMrB,KAAK,EAAEmC;gBACzB;YACF;YACA,OAAOxE,kNAAAA,CAAeoD,GAAG,CAACC,QAAQC,MAAMC;QAC1C;QACAW,KAAIb,MAAM,EAAEC,IAAI,EAAEsB,KAAK,EAAErB,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5ByB,kBAAkBO,MAAM,CAAChC;YAC3B;YACA,OAAOtD,kNAAAA,CAAekE,GAAG,CAACb,QAAQC,MAAMsB,OAAOrB;QACjD;QACAgC,SAAQlC,MAAM;YACZ,MAAMmB,aAAa;YACnBa,UAAU3B,MAAMrB,KAAK,EAAEmC,YAAYS;YACnC,OAAOO,QAAQD,OAAO,CAAClC;QACzB;IACF;IAEAJ,aAAaiB,GAAG,CAACnD,kBAAkBqE;IACnC,OAAOA;AACT;AAEA,2EAA2E;AAC3E,+EAA+E;AAC/E,SAASrC,4CACPhC,gBAAwB,EACxB+B,iBAA0B,EAC1BY,KAAgB;IAEhB,MAAMM,eAAef,aAAaG,GAAG,CAACrC;IACtC,IAAIiD,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAUnB,wBACZpC,4MAAAA,EAA2BK,oBAE3BuB,QAAQC,OAAO,CAACxB;IAEpB,MAAMgE,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7Cb,OAAOC,IAAI,CAACtD,kBAAkBuD,OAAO,CAAC,CAAChB;QACrC,IAAI7C,wMAAAA,CAAoB0B,GAAG,CAACmB,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClE2B,oBAAoBC,IAAI,CAAC5B;QAC3B,OAAO;YACLyB,kBAAkBI,GAAG,CAAC7B;QACxB;IACF;IAEA,MAAM8B,iBAAiB,IAAItB,MAAMG,SAAS;QACxCb,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,AACAyB,kBAAkB5C,GAAG,CAACmB,OACtB,0CAFuE;oBAGvE,MAAMkB,iBAAahE,iNAAAA,EAA6B,UAAU8C;oBAC1DmC,kBAAkB/B,MAAMrB,KAAK,EAAEmC;gBACjC;YACF;YACA,OAAOxE,kNAAAA,CAAeoD,GAAG,CAACC,QAAQC,MAAMC;QAC1C;QACAW,KAAIb,MAAM,EAAEC,IAAI,EAAEsB,KAAK,EAAErB,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5ByB,kBAAkBO,MAAM,CAAChC;YAC3B;YACA,OAAOtD,kNAAAA,CAAekE,GAAG,CAACb,QAAQC,MAAMsB,OAAOrB;QACjD;QACAgC,SAAQlC,MAAM;YACZ,MAAMmB,aAAa;YACnBkB,6BAA6BhC,MAAMrB,KAAK,EAAEmC,YAAYS;YACtD,OAAOO,QAAQD,OAAO,CAAClC;QACzB;IACF;IAEAJ,aAAaiB,GAAG,CAACnD,kBAAkBqE;IACnC,OAAOA;AACT;AAEA,SAASC,UACPhD,KAAyB,EACzBmC,UAAkB,EAClBmB,iBAAiC;IAEjC,MAAM1E,gBAAgBZ,2SAAAA,CAAqBa,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;gBACH,IAAIF,cAAc2E,cAAc,KAAK,MAAM;oBACzC,qEAAqE;oBACrE,qEAAqE;wBACrEzF,gOAAAA,EAAuCc;gBACzC;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEA;QACJ;IACF;IACA,gCAAgC;IAChC,IAAI0E,qBAAqBA,kBAAkBE,MAAM,GAAG,GAAG;QACrDH,6BAA6BrD,OAAOmC,YAAYmB;IAClD,OAAO;QACLF,kBAAkBpD,OAAOmC;IAC3B;AACF;AAEA,MAAMiB,wBAAoB7E,gQAAAA,EACxBkF;AAGF,MAAMJ,mCACJ9E,gQAAAA,EAA4CmF;AAE9C,SAASD,wBACPzD,KAAyB,EACzBmC,UAAkB;IAElB,MAAMwB,SAAS3D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAIwB,MACT,GAAGmC,OAAO,KAAK,EAAExB,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASuB,iCACP1D,KAAyB,EACzBmC,UAAkB,EAClBmB,iBAAgC;IAEhC,MAAMK,SAAS3D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAON,CAPM,IAAIwB,MACT,GAAGmC,OAAO,KAAK,EAAExB,WAAW,EAAE,CAAC,GAC7B,CAAC,0DAA0D,CAAC,GAC5D,CAAC,gEAAgE,CAAC,GAClE,CAAC,mDAAmD,CAAC,GACrD,GAAGyB,4BAA4BN,mBAAmB,EAAE,CAAC,GACrD,CAAC,8DAA8D,CAAC,GAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASM,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWL,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAItF,4LAAAA,CACR,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,CAAC,EAAE,EAAE2F,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/B,KAAK;YACH,OAAO,CAAC,EAAE,EAAEA,UAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIC,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,WAAWL,MAAM,GAAG,GAAGO,IAAK;oBAC9CD,eAAe,CAAC,EAAE,EAAED,UAAU,CAACE,EAAE,CAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,CAAC,QAAQ,EAAED,UAAU,CAACA,WAAWL,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC;gBAC/D,OAAOM;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5337, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/client-page.tsx"], "sourcesContent": ["'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  searchParams,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promises,\n}: {\n  Component: React.ComponentType<any>\n  searchParams: ParsedUrlQuery\n  params: Params\n  promises?: Array<Promise<any>>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n"], "names": ["InvariantError", "ClientPageRoot", "Component", "searchParams", "params", "promises", "window", "workAsyncStorage", "require", "clientSearchParams", "clientParams", "store", "getStore", "createSearchParamsFromClient", "createParamsFromClient", "createRenderSearchParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;;;AAGA,SAASA,cAAc,QAAQ,mCAAkC;AAHjE;;;AAeO,SAASC,eAAe,KAW9B;IAX8B,IAAA,EAC7BC,SAAS,EACTC,YAAY,EACZC,MAAM,EACN,AACAC,QAAQ,EAMT,GAX8B,gDAIgC;IAQ7D,IAAI,OAAOC,WAAW,kBAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQJ,iBAAiBK,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIX,4LAAAA,CACR,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEa,4BAA4B,EAAE,GACpCL,QAAQ;QACVC,qBAAqBI,6BAA6BV,cAAcQ;QAEhE,MAAM,EAAEG,sBAAsB,EAAE,GAC9BN,QAAQ;QACVE,eAAeI,uBAAuBV,QAAQO;QAE9C,OAAA,WAAA,OAAO,8NAAA,EAACT,WAAAA;YAAUE,QAAQM;YAAcP,cAAcM;;IACxD,OAAO;;AAUT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5377, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/client-segment.tsx"], "sourcesContent": ["'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when cacheComponents is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promise,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  params: Params\n  promise?: Promise<any>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n"], "names": ["InvariantError", "ClientSegmentRoot", "Component", "slots", "params", "promise", "window", "workAsyncStorage", "require", "clientParams", "store", "getStore", "createParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;;;AAEA,SAASA,cAAc,QAAQ,mCAAkC;AAFjE;;;AAcO,SAASC,kBAAkB,KAWjC;IAXiC,IAAA,EAChCC,SAAS,EACTC,KAAK,EACLC,MAAM,EACN,AACAC,OAAO,EAMR,GAXiC,iDAI6B;IAQ7D,IAAI,OAAOC,WAAW,kBAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQH,iBAAiBI,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIV,4LAAAA,CACR,uGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEY,sBAAsB,EAAE,GAC9BJ,QAAQ;QACVC,eAAeG,uBAAuBR,QAAQM;QAE9C,OAAA,WAAA,OAAO,8NAAA,EAACR,WAAAA;YAAW,GAAGC,KAAK;YAAEC,QAAQK;;IACvC,OAAO;;AAMT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5414, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/lib/metadata/generate/icon-mark.tsx"], "sourcesContent": ["'use client'\n\n// This is a client component that only renders during SSR,\n// but will be replaced during streaming with an icon insertion script tag.\n// We don't want it to be presented anywhere so it's only visible during streaming,\n// right after the icon meta tags so that browser can pick it up as soon as it's rendered.\n// Note: we don't just emit the script here because we only need the script if it's not in the head,\n// and we need it to be hoistable alongside the other metadata but sync scripts are not hoistable.\nexport const IconMark = () => {\n  if (typeof window !== 'undefined') {\n    return null\n  }\n  return <meta name=\"«nxt-icon»\" />\n}\n"], "names": ["IconMark", "window", "meta", "name"], "mappings": ";;;;;AAAA;;AAQO,MAAMA,WAAW;IACtB,IAAI,OAAOC,WAAW,aAAa;;IAGnC,OAAA,WAAA,OAAO,8NAAA,EAACC,QAAAA;QAAKC,MAAK;;AACpB,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5432, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/client/components/metadata/async-metadata.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense, use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nfunction MetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { error, digest } = use(promise)\n  if (error) {\n    if (digest) {\n      // The error will lose its original digest after passing from server layer to client layer；\n      // We recover the digest property here to override the React created one if original digest exists.\n      ;(error as any).digest = digest\n    }\n    throw error\n  }\n  return null\n}\n\nexport function AsyncMetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  return (\n    <Suspense fallback={null}>\n      <MetadataOutlet promise={promise} />\n    </Suspense>\n  )\n}\n"], "names": ["Suspense", "use", "MetadataOutlet", "promise", "error", "digest", "AsyncMetadataOutlet", "fallback"], "mappings": ";;;;;AAEA,SAASA,QAAQ,EAAEC,GAAG,QAAQ,QAAO;AAFrC;;;AAKA,SAASC,eAAe,KAIvB;IAJuB,IAAA,EACtBC,OAAO,EAGR,GAJuB;IAKtB,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE,GAAGJ,gNAAAA,EAAIE;IAC9B,IAAIC,OAAO;QACT,IAAIC,QAAQ;YACV,2FAA2F;YAC3F,mGAAmG;;YACjGD,MAAcC,MAAM,GAAGA;QAC3B;QACA,MAAMD;IACR;IACA,OAAO;AACT;AAEO,SAASE,oBAAoB,KAInC;IAJmC,IAAA,EAClCH,OAAO,EAGR,GAJmC;IAKlC,OAAA,WAAA,OACE,8NAAA,EAACH,iNAAAA,EAAAA;QAASO,UAAU;kBAClB,WAAA,OAAA,8NAAA,EAACL,gBAAAA;YAAeC,SAASA;;;AAG/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5468, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/dist/src/lib/framework/boundary-components.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactNode } from 'react'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from './boundary-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [ROOT_LAYOUT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n\nexport const RootLayoutBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[\n    ROOT_LAYOUT_BOUNDARY_NAME.slice(0) as typeof ROOT_LAYOUT_BOUNDARY_NAME\n  ]\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME", "NameSpace", "children", "MetadataBoundary", "slice", "ViewportBoundary", "OutletBoundary", "RootLayoutBoundary"], "mappings": ";;;;;;;;;;AAGA,SACEA,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBC,yBAAyB,QACpB,uBAAsB;AAR7B;;AAUA,4EAA4E;AAC5E,iEAAiE;AACjE,MAAMC,YAAY;IAChB,CAACJ,0MAAAA,CAAuB,EAAE,SAAU,EAAEK,QAAQ,EAA2B;QACvE,OAAOA;IACT;IACA,CAACJ,0MAAAA,CAAuB,EAAE,SAAU,EAAEI,QAAQ,EAA2B;QACvE,OAAOA;IACT;IACA,CAACH,wMAAAA,CAAqB,EAAE,SAAU,EAAEG,QAAQ,EAA2B;QACrE,OAAOA;IACT;IACA,CAACF,6MAAAA,CAA0B,EAAE,SAAU,EACrCE,QAAQ,EAGT;QACC,OAAOA;IACT;AACF;AAEO,MAAMC,mBAEX,AADA,4DAC4D,oBADoB;AAEhFF,SAAS,CAACJ,0MAAAA,CAAuBO,KAAK,CAAC,GAAoC,CAAA;AAEtE,MAAMC,mBACX,AACA,4DAA4D,oBADoB;AAEhFJ,SAAS,CAACH,0MAAAA,CAAuBM,KAAK,CAAC,GAAoC,CAAA;AAEtE,MAAME,iBACX,AACA,4DAA4D,oBADoB;AAEhFL,SAAS,CAACF,wMAAAA,CAAqBK,KAAK,CAAC,GAAkC,CAAA;AAElE,MAAMG,qBACX,AACA,4DAA4D,oBADoB;AAEhFN,SAAS,CACPD,6MAAAA,CAA0BI,KAAK,CAAC,GACjC,CAAA", "ignoreList": [0], "debugId": null}}]}