{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/app/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\n\nexport interface User {\n  id: string;\n  email: string;\n  name?: string;\n  createdAt: string;\n  updatedAt: string;\n  posts?: Post[];\n}\n\nexport interface Post {\n  id: string;\n  title: string;\n  content?: string;\n  published: boolean;\n  authorId: string;\n  author?: {\n    id: string;\n    name?: string;\n    email: string;\n  };\n  createdAt: string;\n  updatedAt: string;\n}\n\nclass ApiClient {\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${API_BASE_URL}${endpoint}`;\n    \n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    };\n\n    const response = await fetch(url, config);\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return response.json();\n  }\n\n  // Users\n  async getUsers(): Promise<User[]> {\n    return this.request<User[]>('/api/users');\n  }\n\n  async getUserById(id: string): Promise<User> {\n    return this.request<User>(`/api/users/${id}`);\n  }\n\n  async createUser(userData: { email: string; name?: string }): Promise<User> {\n    return this.request<User>('/api/users', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async updateUser(id: string, userData: { email?: string; name?: string }): Promise<User> {\n    return this.request<User>(`/api/users/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async deleteUser(id: string): Promise<void> {\n    await this.request(`/api/users/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  // Posts\n  async getPosts(): Promise<Post[]> {\n    return this.request<Post[]>('/api/posts');\n  }\n\n  async getPostById(id: string): Promise<Post> {\n    return this.request<Post>(`/api/posts/${id}`);\n  }\n\n  async createPost(postData: {\n    title: string;\n    content?: string;\n    authorId: string;\n    published?: boolean;\n  }): Promise<Post> {\n    return this.request<Post>('/api/posts', {\n      method: 'POST',\n      body: JSON.stringify(postData),\n    });\n  }\n\n  async updatePost(id: string, postData: {\n    title?: string;\n    content?: string;\n    published?: boolean;\n  }): Promise<Post> {\n    return this.request<Post>(`/api/posts/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(postData),\n    });\n  }\n\n  async deletePost(id: string): Promise<void> {\n    await this.request(`/api/posts/${id}`, {\n      method: 'DELETE',\n    });\n  }\n}\n\nexport const apiClient = new ApiClient();\n"], "names": [], "mappings": ";;;;AAAA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AA0BxD,MAAM;IACJ,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,eAAe,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,QAAQ;IACR,MAAM,WAA4B;QAChC,OAAO,IAAI,CAAC,OAAO,CAAS;IAC9B;IAEA,MAAM,YAAY,EAAU,EAAiB;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,WAAW,EAAE,IAAI;IAC9C;IAEA,MAAM,WAAW,QAA0C,EAAiB;QAC1E,OAAO,IAAI,CAAC,OAAO,CAAO,cAAc;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAA2C,EAAiB;QACvF,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,WAAW,EAAE,IAAI,EAAE;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE;YACrC,QAAQ;QACV;IACF;IAEA,QAAQ;IACR,MAAM,WAA4B;QAChC,OAAO,IAAI,CAAC,OAAO,CAAS;IAC9B;IAEA,MAAM,YAAY,EAAU,EAAiB;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,WAAW,EAAE,IAAI;IAC9C;IAEA,MAAM,WAAW,QAKhB,EAAiB;QAChB,OAAO,IAAI,CAAC,OAAO,CAAO,cAAc;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,QAI5B,EAAiB;QAChB,OAAO,IAAI,CAAC,OAAO,CAAO,CAAC,WAAW,EAAE,IAAI,EAAE;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE;YACrC,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/app/components/UserCard.tsx"], "sourcesContent": ["'use client';\n\nimport { User } from '../lib/api';\n\ninterface UserCardProps {\n  user: User;\n  onEdit?: (user: User) => void;\n  onDelete?: (userId: string) => void;\n}\n\nexport default function UserCard({ user, onEdit, onDelete }: UserCardProps) {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n      <div className=\"flex justify-between items-start mb-4\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            {user.name || 'Unnamed User'}\n          </h3>\n          <p className=\"text-gray-600\">{user.email}</p>\n        </div>\n        <div className=\"flex space-x-2\">\n          {onEdit && (\n            <button\n              onClick={() => onEdit(user)}\n              className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n            >\n              Edit\n            </button>\n          )}\n          {onDelete && (\n            <button\n              onClick={() => onDelete(user.id)}\n              className=\"text-red-600 hover:text-red-800 text-sm font-medium\"\n            >\n              Delete\n            </button>\n          )}\n        </div>\n      </div>\n      \n      <div className=\"text-sm text-gray-500\">\n        <p>Created: {new Date(user.createdAt).toLocaleDateString()}</p>\n        {user.posts && (\n          <p className=\"mt-1\">Posts: {user.posts.length}</p>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAUe,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAiB;IACxE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CACX,KAAK,IAAI,IAAI;;;;;;0CAEhB,8OAAC;gCAAE,WAAU;0CAAiB,KAAK,KAAK;;;;;;;;;;;;kCAE1C,8OAAC;wBAAI,WAAU;;4BACZ,wBACC,8OAAC;gCACC,SAAS,IAAM,OAAO;gCACtB,WAAU;0CACX;;;;;;4BAIF,0BACC,8OAAC;gCACC,SAAS,IAAM,SAAS,KAAK,EAAE;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAE;4BAAU,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;oBACvD,KAAK,KAAK,kBACT,8OAAC;wBAAE,WAAU;;4BAAO;4BAAQ,KAAK,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;;;AAKvD", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/app/components/PostCard.tsx"], "sourcesContent": ["'use client';\n\nimport { Post } from '../lib/api';\n\ninterface PostCardProps {\n  post: Post;\n  onEdit?: (post: Post) => void;\n  onDelete?: (postId: string) => void;\n}\n\nexport default function PostCard({ post, onEdit, onDelete }: PostCardProps) {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6 border border-gray-200\">\n      <div className=\"flex justify-between items-start mb-4\">\n        <div className=\"flex-1\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">{post.title}</h3>\n            {post.published ? (\n              <span className=\"px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full\">\n                Published\n              </span>\n            ) : (\n              <span className=\"px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full\">\n                Draft\n              </span>\n            )}\n          </div>\n          {post.content && (\n            <p className=\"text-gray-600 mb-3 line-clamp-3\">{post.content}</p>\n          )}\n          {post.author && (\n            <p className=\"text-sm text-gray-500\">\n              By {post.author.name || post.author.email}\n            </p>\n          )}\n        </div>\n        <div className=\"flex space-x-2 ml-4\">\n          {onEdit && (\n            <button\n              onClick={() => onEdit(post)}\n              className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n            >\n              Edit\n            </button>\n          )}\n          {onDelete && (\n            <button\n              onClick={() => onDelete(post.id)}\n              className=\"text-red-600 hover:text-red-800 text-sm font-medium\"\n            >\n              Delete\n            </button>\n          )}\n        </div>\n      </div>\n      \n      <div className=\"text-sm text-gray-500\">\n        <p>Created: {new Date(post.createdAt).toLocaleDateString()}</p>\n        <p>Updated: {new Date(post.updatedAt).toLocaleDateString()}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAUe,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAiB;IACxE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC,KAAK,KAAK;;;;;;oCAC9D,KAAK,SAAS,iBACb,8OAAC;wCAAK,WAAU;kDAAyE;;;;;6DAIzF,8OAAC;wCAAK,WAAU;kDAA2E;;;;;;;;;;;;4BAK9F,KAAK,OAAO,kBACX,8OAAC;gCAAE,WAAU;0CAAmC,KAAK,OAAO;;;;;;4BAE7D,KAAK,MAAM,kBACV,8OAAC;gCAAE,WAAU;;oCAAwB;oCAC/B,KAAK,MAAM,CAAC,IAAI,IAAI,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;;4BACZ,wBACC,8OAAC;gCACC,SAAS,IAAM,OAAO;gCACtB,WAAU;0CACX;;;;;;4BAIF,0BACC,8OAAC;gCACC,SAAS,IAAM,SAAS,KAAK,EAAE;gCAC/B,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAE;4BAAU,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;kCACxD,8OAAC;;4BAAE;4BAAU,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;AAIhE", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { apiClient, User, Post } from './lib/api';\nimport UserCard from './components/UserCard';\nimport PostCard from './components/PostCard';\n\nexport default function Home() {\n  const [users, setUsers] = useState<User[]>([]);\n  const [posts, setPosts] = useState<Post[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState<'users' | 'posts'>('users');\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const [usersData, postsData] = await Promise.all([\n        apiClient.getUsers(),\n        apiClient.getPosts(),\n      ]);\n      setUsers(usersData);\n      setPosts(postsData);\n    } catch (err) {\n      setError('Failed to fetch data. Make sure the server is running.');\n      console.error('Error fetching data:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteUser = async (userId: string) => {\n    if (confirm('Are you sure you want to delete this user?')) {\n      try {\n        await apiClient.deleteUser(userId);\n        setUsers(users.filter(user => user.id !== userId));\n      } catch (err) {\n        console.error('Error deleting user:', err);\n        alert('Failed to delete user');\n      }\n    }\n  };\n\n  const handleDeletePost = async (postId: string) => {\n    if (confirm('Are you sure you want to delete this post?')) {\n      try {\n        await apiClient.deletePost(postId);\n        setPosts(posts.filter(post => post.id !== postId));\n      } catch (err) {\n        console.error('Error deleting post:', err);\n        alert('Failed to delete post');\n      }\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <header className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Full-Stack App\n          </h1>\n          <p className=\"text-gray-600\">\n            Next.js + Express + Prisma + PostgreSQL\n          </p>\n        </header>\n\n        {/* Tab Navigation */}\n        <div className=\"mb-6\">\n          <nav className=\"flex space-x-8\">\n            <button\n              onClick={() => setActiveTab('users')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'users'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              Users ({users.length})\n            </button>\n            <button\n              onClick={() => setActiveTab('posts')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'posts'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              Posts ({posts.length})\n            </button>\n          </nav>\n        </div>\n\n        {/* Content */}\n        {loading ? (\n          <div className=\"flex justify-center items-center h-64\">\n            <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"></div>\n          </div>\n        ) : error ? (\n          <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n            <div className=\"flex\">\n              <div className=\"ml-3\">\n                <h3 className=\"text-sm font-medium text-red-800\">Error</h3>\n                <div className=\"mt-2 text-sm text-red-700\">\n                  <p>{error}</p>\n                </div>\n                <div className=\"mt-4\">\n                  <button\n                    onClick={fetchData}\n                    className=\"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\"\n                  >\n                    Try Again\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div>\n            {activeTab === 'users' && (\n              <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n                {users.length === 0 ? (\n                  <div className=\"col-span-full text-center py-12\">\n                    <p className=\"text-gray-500\">No users found</p>\n                  </div>\n                ) : (\n                  users.map((user) => (\n                    <UserCard\n                      key={user.id}\n                      user={user}\n                      onDelete={handleDeleteUser}\n                    />\n                  ))\n                )}\n              </div>\n            )}\n\n            {activeTab === 'posts' && (\n              <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n                {posts.length === 0 ? (\n                  <div className=\"col-span-full text-center py-12\">\n                    <p className=\"text-gray-500\">No posts found</p>\n                  </div>\n                ) : (\n                  posts.map((post) => (\n                    <PostCard\n                      key={post.id}\n                      post={post}\n                      onDelete={handleDeletePost}\n                    />\n                  ))\n                )}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAS,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAS,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAgB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAoB;IAE9D,IAAA,kNAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,CAAC,WAAW,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC/C,8HAAS,CAAC,QAAQ;gBAClB,8HAAS,CAAC,QAAQ;aACnB;YACD,SAAS;YACT,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,+CAA+C;YACzD,IAAI;gBACF,MAAM,8HAAS,CAAC,UAAU,CAAC;gBAC3B,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC5C,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM;YACR;QACF;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,+CAA+C;YACzD,IAAI;gBACF,MAAM,8HAAS,CAAC,UAAU,CAAC;gBAC3B,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC5C,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAM/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,UACV,kCACA,wDACJ;;oCACH;oCACS,MAAM,MAAM;oCAAC;;;;;;;0CAEvB,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,UACV,kCACA,wDACJ;;oCACH;oCACS,MAAM,MAAM;oCAAC;;;;;;;;;;;;;;;;;;gBAM1B,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;2BAEf,sBACF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAG;;;;;;;;;;;8CAEN,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;yCAQT,8OAAC;;wBACE,cAAc,yBACb,8OAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,KAAK,kBAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;uCAG/B,MAAM,GAAG,CAAC,CAAC,qBACT,8OAAC,yIAAQ;oCAEP,MAAM;oCACN,UAAU;mCAFL,KAAK,EAAE;;;;;;;;;;wBASrB,cAAc,yBACb,8OAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,KAAK,kBAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;uCAG/B,MAAM,GAAG,CAAC,CAAC,qBACT,8OAAC,yIAAQ;oCAEP,MAAM;oCACN,UAAU;mCAFL,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAalC", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent6-gemini/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}