# Full-Stack Application

Uma aplicação full-stack moderna usando Next.js, Express, Prisma e PostgreSQL.

## 🚀 Tecnologias

- **Frontend**: Next.js 15 com TypeScript e Tailwind CSS
- **Backend**: Express.js com TypeScript
- **Database**: PostgreSQL com Prisma ORM
- **Styling**: Tailwind CSS

## 📁 Estrutura do Projeto

```
/
├── /app                    # Next.js App Router
│   ├── /components         # Componentes React
│   ├── /lib               # Funções utilitárias
│   └── page.tsx           # Página principal
├── /server                # Backend Express
│   ├── /routes            # Rotas da API
│   ├── /controllers       # Lógica de negócio
│   └── index.ts           # Servidor principal
├── /prisma
│   └── schema.prisma      # Esquema do banco de dados
└── package.json           # Dependências e scripts
```

## 🛠️ Configuração

### 1. Instalar dependências

```bash
npm install
```

### 2. Configurar banco de dados

1. Crie um banco PostgreSQL
2. Copie o arquivo `.env` e configure a URL do banco:

```env
DATABASE_URL="postgresql://username:password@localhost:5432/mydb?schema=public"
PORT=3001
NODE_ENV=development
```

### 3. Configurar Prisma

```bash
# Gerar o cliente Prisma
npm run db:generate

# Aplicar o schema ao banco (desenvolvimento)
npm run db:push

# Ou criar e aplicar migrações (produção)
npm run db:migrate
```

## 🚀 Executar a aplicação

### Desenvolvimento

Execute frontend e backend simultaneamente:

```bash
npm run dev
```

Ou execute separadamente:

```bash
# Frontend (Next.js) - http://localhost:3000
npm run dev:app

# Backend (Express) - http://localhost:3001
npm run dev:server
```

### Produção

```bash
# Build
npm run build
npm run build:server

# Start
npm start
npm run start:server
```

## 📊 Scripts disponíveis

- `npm run dev` - Executa app e server em desenvolvimento
- `npm run dev:app` - Executa apenas o Next.js
- `npm run dev:server` - Executa apenas o Express
- `npm run build` - Build do Next.js
- `npm run build:server` - Build do servidor Express
- `npm run db:generate` - Gera o cliente Prisma
- `npm run db:push` - Aplica schema ao banco (dev)
- `npm run db:migrate` - Cria e aplica migrações
- `npm run db:studio` - Abre Prisma Studio

## 🔗 API Endpoints

### Users
- `GET /api/users` - Lista todos os usuários
- `GET /api/users/:id` - Busca usuário por ID
- `POST /api/users` - Cria novo usuário
- `PUT /api/users/:id` - Atualiza usuário
- `DELETE /api/users/:id` - Remove usuário

### Posts
- `GET /api/posts` - Lista todos os posts
- `GET /api/posts/:id` - Busca post por ID
- `POST /api/posts` - Cria novo post
- `PUT /api/posts/:id` - Atualiza post
- `DELETE /api/posts/:id` - Remove post

## 🎨 Frontend

O frontend foi construído com:
- **Next.js 15** com App Router
- **TypeScript** para type safety
- **Tailwind CSS** para styling
- **React Hooks** para gerenciamento de estado
- **Fetch API** para comunicação com backend

## 🔧 Backend

O backend inclui:
- **Express.js** com TypeScript
- **Prisma ORM** para database
- **CORS** habilitado
- **Error handling** middleware
- **Graceful shutdown**

## 📝 Próximos passos

- [ ] Adicionar autenticação
- [ ] Implementar validação de dados
- [ ] Adicionar testes
- [ ] Configurar CI/CD
- [ ] Adicionar logging
- [ ] Implementar cache
- [ ] Adicionar documentação da API (Swagger)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request
