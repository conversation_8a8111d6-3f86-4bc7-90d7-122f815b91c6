{"root": ["../bin/concurrently.spec.ts", "../bin/concurrently.ts", "../bin/read-package.ts", "../declarations/intl.d.ts", "../src/assert.spec.ts", "../src/assert.ts", "../src/command.spec.ts", "../src/command.ts", "../src/completion-listener.spec.ts", "../src/completion-listener.ts", "../src/concurrently.spec.ts", "../src/concurrently.ts", "../src/date-format.spec.ts", "../src/date-format.ts", "../src/defaults.ts", "../src/index.ts", "../src/jsonc.spec.ts", "../src/jsonc.ts", "../src/logger.spec.ts", "../src/logger.ts", "../src/observables.spec.ts", "../src/observables.ts", "../src/output-writer.spec.ts", "../src/output-writer.ts", "../src/prefix-color-selector.spec.ts", "../src/prefix-color-selector.ts", "../src/spawn.spec.ts", "../src/spawn.ts", "../src/utils.spec.ts", "../src/utils.ts", "../src/command-parser/command-parser.ts", "../src/command-parser/expand-arguments.spec.ts", "../src/command-parser/expand-arguments.ts", "../src/command-parser/expand-shortcut.spec.ts", "../src/command-parser/expand-shortcut.ts", "../src/command-parser/expand-wildcard.spec.ts", "../src/command-parser/expand-wildcard.ts", "../src/command-parser/strip-quotes.spec.ts", "../src/command-parser/strip-quotes.ts", "../src/fixtures/create-mock-instance.ts", "../src/fixtures/fake-command.ts", "../src/flow-control/flow-controller.ts", "../src/flow-control/input-handler.spec.ts", "../src/flow-control/input-handler.ts", "../src/flow-control/kill-on-signal.spec.ts", "../src/flow-control/kill-on-signal.ts", "../src/flow-control/kill-others.spec.ts", "../src/flow-control/kill-others.ts", "../src/flow-control/log-error.spec.ts", "../src/flow-control/log-error.ts", "../src/flow-control/log-exit.spec.ts", "../src/flow-control/log-exit.ts", "../src/flow-control/log-output.spec.ts", "../src/flow-control/log-output.ts", "../src/flow-control/log-timings.spec.ts", "../src/flow-control/log-timings.ts", "../src/flow-control/logger-padding.spec.ts", "../src/flow-control/logger-padding.ts", "../src/flow-control/output-error-handler.spec.ts", "../src/flow-control/output-error-handler.ts", "../src/flow-control/restart-process.spec.ts", "../src/flow-control/restart-process.ts", "../src/flow-control/teardown.spec.ts", "../src/flow-control/teardown.ts"], "version": "5.9.2"}