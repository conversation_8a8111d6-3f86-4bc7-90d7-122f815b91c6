{"name": "concurrently", "version": "9.2.1", "description": "Run commands concurrently", "main": "index.js", "types": "index.d.ts", "type": "commonjs", "bin": {"concurrently": "./dist/bin/concurrently.js", "conc": "./dist/bin/concurrently.js"}, "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/open-cli-tools/concurrently.git"}, "funding": "https://github.com/open-cli-tools/concurrently?sponsor=1", "keywords": ["bash", "concurrent", "parallel", "concurrently", "command", "sh"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"chalk": "4.1.2", "rxjs": "7.8.2", "shell-quote": "1.8.3", "supports-color": "8.1.1", "tree-kill": "1.2.2", "yargs": "17.7.2"}, "devDependencies": {"@eslint/js": "^9.33.0", "@hirez_io/observer-spy": "^2.2.0", "@types/node": "^18.19.123", "@types/shell-quote": "^1.7.5", "@types/supports-color": "^8.1.3", "@types/yargs": "^17.0.33", "@vitest/coverage-v8": "^3.2.4", "@vitest/eslint-plugin": "^1.3.4", "coveralls-next": "^5.0.0", "ctrlc-wrapper": "^0.0.5", "esbuild": "~0.25.9", "eslint": "^9.33.0", "eslint-config-flat-gitignore": "^2.1.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import-lite": "^0.3.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "16.3.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "prettier": "^3.6.2", "safe-publish-latest": "^2.0.0", "string-argv": "^0.3.2", "typescript": "~5.9.2", "typescript-eslint": "^8.40.0", "vitest": "^3.2.4"}, "files": ["dist", "index.js", "index.d.ts", "index.mjs", "index.d.mts", "!**/fixtures", "!**/*.spec.js", "!**/*.spec.d.ts", "docs"], "lint-staged": {"*.?(m){js,ts}": "eslint --fix", "*.{json,y?(a)ml,md}": "prettier --write"}, "scripts": {"build": "tsc --build", "postbuild": "chmod +x dist/bin/concurrently.js", "clean": "tsc --build --clean", "format": "prettier --check '**/*.{json,y?(a)ml,md}'", "format:fix": "pnpm run format --write", "lint": "eslint", "lint:fix": "pnpm run lint --fix", "report-coverage": "cat coverage/lcov.info | coveralls", "test": "vitest --project unit", "test:smoke": "vitest run --project smoke"}}